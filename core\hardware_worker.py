# -*- coding: utf-8 -*-
"""
硬件信息获取工作线程
使用QThread实现异步硬件信息获取，防止UI阻塞
"""
from PySide6.QtCore import QThread, Signal, QObject
from typing import Dict, Any
import time
from .hardware_info import (
    get_system_info,
    get_cpu_info,
    get_memory_info,
    get_disk_info,
    get_gpu_info,
    get_motherboard_info,
    get_all_hardware_info,
)


class HardwareInfoWorker(QThread):
    """硬件信息获取工作线程"""

    # 信号定义
    progress_updated = Signal(str, int)  # 进度更新信号 (当前任务, 进度百分比)
    info_ready = Signal(str, dict)  # 单个信息获取完成信号 (信息类型, 信息数据)
    all_info_ready = Signal(dict)  # 所有信息获取完成信号 (完整硬件信息)
    error_occurred = Signal(str)  # 错误发生信号 (错误信息)
    finished = Signal()  # 完成信号

    def __init__(self, parent=None):
        super().__init__(parent)
        self.is_cancelled = False
        self.info_types = [
            ("system", "系统信息", get_system_info),
            ("motherboard", "主板信息", get_motherboard_info),
            ("cpu", "处理器信息", get_cpu_info),
            ("memory", "内存信息", get_memory_info),
            ("disk", "存储设备", get_disk_info),
            ("gpu", "显卡信息", get_gpu_info),
        ]

    def run(self):
        """执行硬件信息获取"""
        try:
            hardware_info = {}
            total_steps = len(self.info_types)

            for i, (info_type, display_name, get_func) in enumerate(self.info_types):
                if self.is_cancelled:
                    break

                # 更新进度
                progress = int((i / total_steps) * 100)
                try:
                    self.progress_updated.emit(f"正在获取{display_name}...", progress)
                except RuntimeError:
                    # 对象可能已经被删除
                    return

                try:
                    # 获取信息
                    info = get_func()
                    hardware_info[info_type] = info

                    # 发送单个信息完成信号
                    if not self.is_cancelled:
                        try:
                            self.info_ready.emit(info_type, info)
                        except RuntimeError:
                            # 对象可能已经被删除
                            return
                except Exception as e:
                    if not self.is_cancelled:
                        try:
                            self.error_occurred.emit(
                                f"获取{display_name}失败: {str(e)}"
                            )
                        except RuntimeError:
                            # 对象可能已经被删除
                            return
                    hardware_info[info_type] = {"error": str(e)}

            if not self.is_cancelled:
                # 完成进度
                try:
                    self.progress_updated.emit("硬件信息获取完成", 100)
                except RuntimeError:
                    # 对象可能已经被删除
                    return

                # 发送完整信息
                try:
                    self.all_info_ready.emit(hardware_info)
                except RuntimeError:
                    # 对象可能已经被删除
                    return

        except Exception as e:
            try:
                self.error_occurred.emit(f"硬件信息获取过程中发生错误: {str(e)}")
            except RuntimeError:
                # 对象可能已经被删除
                pass

        finally:
            # 清理资源
            self.is_cancelled = False
            try:
                self.finished.emit()
            except RuntimeError:
                # 对象可能已经被删除
                pass

    def cancel(self):
        """取消硬件信息获取"""
        self.is_cancelled = True


class HardwareInfoManager(QObject):
    """硬件信息管理器"""

    # 信号定义
    progress_updated = Signal(str, int)  # 进度更新信号
    info_updated = Signal(str, dict)  # 信息更新信号
    all_info_ready = Signal(dict)  # 所有信息就绪信号
    error_occurred = Signal(str)  # 错误信号

    def __init__(self, parent=None):
        super().__init__(parent)
        self.worker = None
        self.cached_info = {}

    def get_hardware_info_async(self):
        """异步获取硬件信息"""
        if self.worker and self.worker.isRunning():
            self.cancel_current_operation()

        self.worker = HardwareInfoWorker(self)

        # 连接信号
        self.worker.progress_updated.connect(self.progress_updated)
        self.worker.info_ready.connect(self._on_info_ready)
        self.worker.all_info_ready.connect(self._on_all_info_ready)
        self.worker.error_occurred.connect(self.error_occurred)
        self.worker.finished.connect(self._on_worker_finished)

        # 启动线程
        self.worker.start()

    def _on_info_ready(self, info_type: str, info: dict):
        """处理单个信息获取完成"""
        self.cached_info[info_type] = info
        self.info_updated.emit(info_type, info)

    def _on_all_info_ready(self, hardware_info: dict):
        """处理所有信息获取完成"""
        self.cached_info = hardware_info
        self.all_info_ready.emit(hardware_info)

    def _on_worker_finished(self):
        """处理工作线程完成"""
        if self.worker:
            self.worker = None

    def get_cached_info(self, info_type: str = None) -> dict:
        """获取缓存的硬件信息"""
        if info_type is None:
            return self.cached_info
        return self.cached_info.get(info_type, {})

    def cancel_current_operation(self):
        """取消当前操作"""
        if self.worker and self.worker.isRunning():
            self.worker.cancel()

            # 等待线程响应取消请求
            start_time = time.time()
            while self.worker.isRunning() and time.time() - start_time < 1.0:
                QThread.msleep(50)  # 短暂等待线程响应

            # 如果线程仍在运行，强制终止
            if self.worker.isRunning():
                self.worker.terminate()
                self.worker.wait(1000)  # 等待最多1秒

    def cleanup(self):
        """清理资源"""
        self.cancel_current_operation()
        self.cached_info.clear()
