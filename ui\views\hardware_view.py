# -*- coding: utf-8 -*-
"""
硬件信息视图模块
提供现代化的硬件信息卡片式布局显示
"""

import json
import os
from datetime import datetime

from PySide6.QtCore import QSize, Qt, QTimer, Slot
from PySide6.QtGui import QClipboard
from PySide6.QtWidgets import (
    QApplication,
    QFrame,
    QGridLayout,
    QHBoxLayout,
    QLabel,
    QScrollArea,
    QSizePolicy,
    QVBoxLayout,
    QWidget,
)
from qfluentwidgets import (
    BodyLabel,
    CaptionLabel,
    CardWidget,
    FluentIcon,
    InfoBar,
    InfoBarPosition,
    ProgressRing,
    PushButton,
    SingleDirectionScrollArea,
    TitleLabel,
    TransparentToolButton,
)

from core.hardware_worker import HardwareInfoManager


class HardwareInfoCard(CardWidget):
    """硬件信息卡片组件"""

    def __init__(self, title, icon, parent=None):
        super().__init__(parent)
        self.title = title
        self.icon = icon
        self.info_data = {}
        self.is_loading = True
        self.setup_ui()
        self.show_loading()

    def setup_ui(self):
        """初始化界面"""
        # 设置卡片固定大小
        self.setFixedSize(320, 220)  # 固定宽度320px，高度220px

        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(16, 16, 16, 16)
        main_layout.setSpacing(10)

        # 创建标题区域
        title_layout = QHBoxLayout()
        title_layout.setSpacing(8)

        # 标题文本
        title_label = BodyLabel(self.title)
        title_label.setStyleSheet("font-weight: 600; font-size: 14px;")
        title_layout.addWidget(title_label)

        title_layout.addStretch()

        # 复制按钮
        self.copy_btn = TransparentToolButton(FluentIcon.COPY, self)
        self.copy_btn.setFixedSize(32, 32)
        self.copy_btn.clicked.connect(self.copy_info)
        self.copy_btn.setToolTip("复制此项信息")
        title_layout.addWidget(self.copy_btn)

        main_layout.addLayout(title_layout)

        # 创建内容区域
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.content_layout.setSpacing(6)

        main_layout.addWidget(self.content_widget, 1)

    def show_loading(self):
        """显示加载状态"""
        self.clear_content()

        # 创建加载布局
        loading_layout = QVBoxLayout()
        loading_layout.setAlignment(Qt.AlignCenter)
        loading_layout.setSpacing(12)

        # 加载动画
        progress_ring = ProgressRing(self)
        progress_ring.setFixedSize(32, 32)

        # 加载文本
        loading_label = CaptionLabel("正在获取信息...", self)
        loading_label.setAlignment(Qt.AlignCenter)

        loading_layout.addWidget(progress_ring, 0, Qt.AlignCenter)
        loading_layout.addWidget(loading_label, 0, Qt.AlignCenter)

        loading_widget = QWidget()
        loading_widget.setLayout(loading_layout)
        self.content_layout.addWidget(loading_widget)

    def clear_content(self):
        """清除内容区域"""
        while self.content_layout.count():
            child = self.content_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

    def update_info(self, info_data):
        """更新硬件信息"""
        try:
            self.info_data = info_data
            self.is_loading = False
            self.clear_content()

            if not info_data or "error" in info_data:
                self.show_no_data()
                return

            # 格式化并显示信息
            formatted_info = self.format_hardware_info(info_data)
            for key, value in formatted_info.items():
                self.add_info_row(key, value)
        except Exception as e:
            print(f"更新硬件卡片信息时出错: {e}")
            self.show_no_data()

    def show_no_data(self):
        """显示无数据状态"""
        no_data_layout = QVBoxLayout()
        no_data_layout.setAlignment(Qt.AlignCenter)

        no_data_label = CaptionLabel("暂无数据", self)
        no_data_label.setAlignment(Qt.AlignCenter)

        no_data_layout.addWidget(no_data_label)

        no_data_widget = QWidget()
        no_data_widget.setLayout(no_data_layout)
        self.content_layout.addWidget(no_data_widget)

    def add_info_row(self, key, value):
        """添加信息行"""
        row_layout = QHBoxLayout()
        row_layout.setContentsMargins(0, 0, 0, 0)
        row_layout.setSpacing(8)

        # 键标签
        key_label = CaptionLabel(key + ":", self)
        key_label.setStyleSheet("font-size: 12px;")
        key_label.setFixedWidth(80)

        # 值标签
        value_label = CaptionLabel(str(value), self)
        value_label.setStyleSheet("font-size: 12px; font-weight: 500;")
        value_label.setWordWrap(True)

        # 设置主题适配的文本颜色
        from PySide6.QtGui import QColor

        key_label.setTextColor(
            QColor(102, 102, 102), QColor(153, 153, 153)
        )  # 浅色主题, 深色主题
        value_label.setTextColor(
            QColor(51, 51, 51), QColor(255, 255, 255)
        )  # 浅色主题, 深色主题

        row_layout.addWidget(key_label)
        row_layout.addWidget(value_label, 1)

        row_widget = QWidget()
        row_widget.setLayout(row_layout)
        self.content_layout.addWidget(row_widget)

    def format_hardware_info(self, info_data):
        """格式化硬件信息"""
        if not info_data:
            return {}

        formatted = {}

        # 根据标题确定信息类型
        if self.title == "系统信息":
            formatted = {
                "系统": info_data.get("caption", "未知"),
                "版本": info_data.get("version", "未知"),
                "架构": info_data.get("os_architecture", "未知"),
                "主机名": info_data.get("hostname", "未知"),
                "启动时间": info_data.get("last_boot_time", "未知"),
            }
        elif self.title == "处理器信息":
            processors = info_data.get("processors", [])
            if processors and len(processors) > 0:
                cpu = processors[0]
                formatted = {
                    "型号": cpu.get("name", "未知"),
                    "制造商": cpu.get("manufacturer", "未知"),
                    "核心数": str(cpu.get("cores", "未知")),
                    "线程数": str(cpu.get("logical_processors", "未知")),
                    "频率": cpu.get("current_clock_speed", "未知"),
                }
        elif self.title == "内存信息":
            # 格式化总容量显示
            total_capacity = info_data.get("total_capacity", "未知")
            slots = info_data.get("physical_slots", [])

            if slots and total_capacity != "未知":
                # 构建总容量详细信息：总容量（容量1+容量2+...）
                capacity_details = "+".join(
                    [slot.get("capacity", "未知") for slot in slots]
                )
                formatted["总容量"] = f"{total_capacity}（{capacity_details}）"
            else:
                formatted["总容量"] = total_capacity

            # 格式化每个插槽信息：型号 - 频率 - 容量
            if slots:
                for i, slot in enumerate(slots):  # 显示所有内存插槽
                    part_number = slot.get("part_number", "未知")
                    speed = slot.get("speed", "未知")
                    capacity = slot.get("capacity", "未知")
                    formatted[f"插槽{i+1}内存规格"] = (
                        f"{part_number} - {speed} - {capacity}"
                    )
        elif self.title == "存储设备":
            disks = info_data.get("physical_disks", [])
            if disks:
                for i, disk in enumerate(disks):  # 显示所有存储设备
                    model = disk.get("model", "未知")
                    size = disk.get("size", "未知")
                    formatted[f"硬盘{i+1}"] = f"{model} （{size}）"
        elif self.title == "显卡信息":
            gpus = info_data.get("gpus", [])
            if gpus:
                for i, gpu in enumerate(gpus):  # 显示所有显卡
                    formatted[f"显卡{i+1}"] = gpu.get("name", "未知")
                    formatted[f"驱动{i+1}"] = gpu.get("driver_version", "未知")

                    # 合并分辨率和刷新率显示
                    resolution = gpu.get("resolution", "未知")
                    refresh_rate = gpu.get("current_refresh_rate", "未知")
                    if resolution != "未知" and refresh_rate != "未知":
                        # 移除刷新率中的" Hz"后缀，重新格式化
                        refresh_value = refresh_rate.replace(" Hz", "")
                        formatted[f"分辨率{i+1}"] = f"{resolution}@{refresh_value}Hz"
                    else:
                        formatted[f"分辨率{i+1}"] = resolution
        elif self.title == "主板信息":
            motherboard = info_data.get("motherboard", {})
            bios = info_data.get("bios", {})
            if motherboard:
                formatted = {
                    "制造商": motherboard.get("manufacturer", "未知"),
                    "型号": motherboard.get("product", "未知"),
                }
            if bios:
                formatted.update(
                    {
                        "BIOS版本": bios.get("version", "未知"),
                        "发布日期": bios.get("release_date", "未知"),
                    }
                )

        return formatted

    def copy_info(self):
        """复制硬件信息到剪贴板"""
        if not self.info_data:
            InfoBar.warning(
                title="复制失败",
                content="暂无信息可复制",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=2000,
                parent=self.parent(),
            )
            return

        # 格式化信息为文本
        formatted_info = self.format_hardware_info(self.info_data)
        info_lines = [f"{key}: {value}" for key, value in formatted_info.items()]
        info_text = f"{self.title}\n" + "\n".join(info_lines)

        # 复制到剪贴板
        clipboard = QApplication.clipboard()
        clipboard.setText(info_text)

        InfoBar.success(
            title="复制成功",
            content=f"{self.title}已复制到剪贴板",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=1500,
            parent=self.parent(),
        )


class HardwareView(SingleDirectionScrollArea):
    """硬件信息主视图"""

    def __init__(self, parent=None):
        super().__init__(orient=Qt.Orientation.Vertical, parent=parent)
        self.setObjectName("HardwareView")
        self.setWidgetResizable(True)
        self.enableTransparentBackground()
        self.setStyleSheet("QScrollArea { border: none; background: transparent; }")
        self.viewport().setStyleSheet("background: transparent;")

        self.hardware_manager = HardwareInfoManager(self)
        self.hardware_cards = {}
        self.hardware_card_widgets = []  # 存储硬件卡片组件
        self.is_loaded = False
        self.current_cards_per_row = 0  # 跟踪当前列数，避免不必要的重新排列
        self.setup_ui()
        self.connect_signals()

    def setup_ui(self):
        """初始化界面"""
        # 创建内容容器
        content_widget = QWidget()
        content_widget.setStyleSheet("background: transparent;")
        # 设置大小策略，防止内容被拉伸
        content_widget.setSizePolicy(
            QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Minimum
        )

        # 创建主布局
        main_layout = QVBoxLayout(content_widget)
        main_layout.setContentsMargins(24, 24, 24, 24)
        main_layout.setSpacing(20)

        # 页面标题 - 使用TitleLabel + CaptionLabel标准格式
        title_widget = QWidget()
        title_layout = QVBoxLayout(title_widget)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(4)

        title_label = TitleLabel("硬件信息", self)
        subtitle_label = CaptionLabel("查看系统硬件详细信息", self)

        title_layout.addWidget(title_label)
        title_layout.addWidget(subtitle_label)

        main_layout.addWidget(title_widget)

        # 创建操作按钮区域
        self.create_action_buttons_card(main_layout)

        # 创建硬件卡片区域
        self.create_hardware_cards_direct(main_layout)

        # 设置为滚动区域的内容
        self.setWidget(content_widget)

    def create_action_buttons_card(self, main_layout):
        """创建操作按钮卡片"""
        # 创建操作按钮卡片
        action_card = CardWidget()
        action_layout = QHBoxLayout(action_card)
        action_layout.setContentsMargins(16, 16, 16, 16)
        action_layout.setSpacing(12)

        # 添加说明文本
        info_label = BodyLabel("操作")
        action_layout.addWidget(info_label)

        action_layout.addStretch()

        # 复制所有按钮
        self.copy_all_btn = PushButton("复制所有", self)
        self.copy_all_btn.setIcon(FluentIcon.COPY)
        self.copy_all_btn.clicked.connect(self.copy_all_info)

        # 导出按钮
        self.export_btn = PushButton("导出", self)
        self.export_btn.setIcon(FluentIcon.SAVE)
        self.export_btn.clicked.connect(self.export_info)

        action_layout.addWidget(self.copy_all_btn)
        action_layout.addWidget(self.export_btn)

        main_layout.addWidget(action_card)

    def create_hardware_cards_direct(self, main_layout):
        """直接创建硬件信息卡片"""
        # 硬件类别配置 - 调整顺序：主板信息移到系统信息之后，移除网络适配器
        hardware_categories = [
            ("系统信息", FluentIcon.SETTING),
            ("主板信息", FluentIcon.CHECKBOX),
            ("处理器信息", FluentIcon.SPEED_HIGH),
            ("内存信息", FluentIcon.ALBUM),
            ("存储设备", FluentIcon.FOLDER),
            ("显卡信息", FluentIcon.VIDEO),
        ]

        # 创建自适应网格布局
        self.cards_container = QWidget()
        self.grid_layout = QGridLayout(self.cards_container)
        self.grid_layout.setContentsMargins(0, 0, 0, 0)
        self.grid_layout.setHorizontalSpacing(16)
        self.grid_layout.setVerticalSpacing(16)

        # 映射关系：界面显示名称 -> 工作线程信息类型 - 移除网络适配器映射
        category_mapping = {
            "系统信息": "system",
            "主板信息": "motherboard",
            "处理器信息": "cpu",
            "内存信息": "memory",
            "存储设备": "disk",
            "显卡信息": "gpu",
        }

        # 创建卡片并存储
        self.hardware_card_widgets = []
        for category, icon in hardware_categories:
            card = HardwareInfoCard(category, icon, self)
            key = category_mapping.get(category)
            self.hardware_cards[key] = card
            self.hardware_card_widgets.append(card)

        # 初始布局
        self._arrange_hardware_cards()

        main_layout.addWidget(self.cards_container)

    def _arrange_hardware_cards(self):
        """智能重新排列硬件卡片 - 只在列数变化时才重新排列"""
        if not hasattr(self, "hardware_card_widgets") or not self.hardware_card_widgets:
            return

        # 计算每行卡片数量（基于窗口宽度）
        cards_per_row = self._calculate_hardware_cards_per_row()

        # 只在列数真正发生变化时才重新排列，避免不必要的布局重建
        if cards_per_row == self.current_cards_per_row:
            return

        # 更新当前列数
        self.current_cards_per_row = cards_per_row

        # 温和地重新排列卡片 - 先移除再重新添加，避免完全清空布局
        for i in reversed(range(self.grid_layout.count())):
            item = self.grid_layout.itemAt(i)
            if item and item.widget():
                self.grid_layout.removeItem(item)

        # 重新排列卡片到新的网格位置
        for i, card in enumerate(self.hardware_card_widgets):
            row = i // cards_per_row
            col = i % cards_per_row
            self.grid_layout.addWidget(card, row, col)

    def _calculate_hardware_cards_per_row(self):
        """计算每行能显示的硬件卡片数量"""
        # 获取可用宽度（减去边距、滚动条等）
        available_width = self.width() - 48  # 主布局边距24*2

        # 硬件卡片的固定宽度
        card_width = 320  # 硬件卡片的固定宽度
        card_spacing = 16  # 卡片间距

        # 如果宽度太小，最少显示1个
        if available_width < card_width:
            return 1

        # 动态计算能容纳的卡片数量，不限制最大列数
        cards_per_row = max(
            1, (available_width + card_spacing) // (card_width + card_spacing)
        )

        return cards_per_row

    def connect_signals(self):
        """连接信号"""
        self.hardware_manager.all_info_ready.connect(self.on_hardware_info_ready)
        self.hardware_manager.info_updated.connect(self.on_single_info_ready)
        self.hardware_manager.error_occurred.connect(self.on_error_occurred)

    def showEvent(self, event):
        """界面显示时的事件处理"""
        super().showEvent(event)
        if not self.is_loaded:
            self.is_loaded = True
            # 延迟加载硬件信息，确保界面先显示
            QTimer.singleShot(100, self.load_hardware_info)

    def load_hardware_info(self):
        """加载硬件信息"""
        self.hardware_manager.get_hardware_info_async()

    @Slot(dict)
    def on_hardware_info_ready(self, hardware_info):
        """处理硬件信息就绪"""
        try:
            for category, info in hardware_info.items():
                if category in self.hardware_cards:
                    try:
                        self.hardware_cards[category].update_info(info)
                    except Exception as e:
                        print(f"更新{category}卡片时出错: {e}")
        except Exception as e:
            print(f"处理硬件信息时出错: {e}")

    @Slot(str, dict)
    def on_single_info_ready(self, category, info):
        """处理单个硬件信息就绪"""
        try:
            if category in self.hardware_cards:
                try:
                    self.hardware_cards[category].update_info(info)
                except Exception as e:
                    print(f"更新{category}卡片时出错: {e}")
        except Exception as e:
            print(f"处理单个硬件信息时出错: {e}")

    @Slot(str)
    def on_error_occurred(self, error_message):
        """处理错误"""
        try:
            InfoBar.error(
                title="获取硬件信息失败",
                content=error_message,
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=5000,
                parent=self,
            )
        except Exception as e:
            print(f"显示错误信息时出错: {e}")

    def copy_all_info(self):
        """复制所有硬件信息"""
        all_info = []
        for card in self.hardware_cards.values():
            if card.info_data:
                formatted_info = card.format_hardware_info(card.info_data)
                info_lines = [
                    f"{key}: {value}" for key, value in formatted_info.items()
                ]
                info_text = f"{card.title}\n" + "\n".join(info_lines)
                all_info.append(info_text)

        if all_info:
            clipboard = QApplication.clipboard()
            clipboard.setText("\n\n".join(all_info))

            InfoBar.success(
                title="复制成功",
                content="所有硬件信息已复制到剪贴板",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=2000,
                parent=self,
            )
        else:
            InfoBar.warning(
                title="复制失败",
                content="暂无硬件信息可复制",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=2000,
                parent=self,
            )

    def export_info(self):
        """导出硬件信息到文件"""
        export_data = {}
        for key, card in self.hardware_cards.items():
            if card.info_data:
                export_data[key] = card.info_data

        if not export_data:
            InfoBar.warning(
                title="导出失败",
                content="暂无硬件信息可导出",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=2000,
                parent=self,
            )
            return

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"hardware_info_{timestamp}.json"
        filepath = os.path.join(os.path.expanduser("~"), "Desktop", filename)

        try:
            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)

            InfoBar.success(
                title="导出成功",
                content=f"硬件信息已导出到桌面: {filename}",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=3000,
                parent=self,
            )
        except Exception as e:
            InfoBar.error(
                title="导出失败",
                content=f"导出过程中发生错误: {str(e)}",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=3000,
                parent=self,
            )

    def resizeEvent(self, event):
        """窗口大小变化事件，用于调整网格布局"""
        super().resizeEvent(event)
        # 重新排列硬件卡片以适应新的窗口大小
        if hasattr(self, "hardware_card_widgets"):
            self._arrange_hardware_cards()

    def closeEvent(self, event):
        """窗口关闭事件"""
        if hasattr(self, "hardware_manager"):
            self.hardware_manager.cleanup()
        super().closeEvent(event)
