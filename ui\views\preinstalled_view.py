# -*- coding: utf-8 -*-
"""
预装应用管理视图模块 - 严格按照需求文档实现
"""
import os
import subprocess
import time
from typing import Dict, List

from PySide6.QtCore import Qt, QThread, Signal
from PySide6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QStackedWidget,
    QScrollArea,
    QFrame,
    QSizePolicy,
)

from qfluentwidgets import (
    HeaderCardWidget,
    CardWidget,
    SimpleCardWidget,
    CheckBox,
    PushButton,
    SegmentedWidget,
    ProgressRing,
    BodyLabel,
    TitleLabel,
    CaptionLabel,
    InfoBar,
    InfoBarPosition,
    FluentIcon,
    SingleDirectionScrollArea,
)

from config.appx_packages import APPX_PACKAGES
from config.onedrive_cleanup import ONE_DRIVE_CLEANUP
from core.app_manager import AppManagerWorker, AppManager


class AppTab(QWidget):
    """应用选项卡基类"""

    def __init__(self, app_category: str, apps_dict: Dict[str, str]):
        super().__init__()
        self.app_category = app_category
        self.apps_dict = apps_dict
        self.checkboxes = {}
        self._init_ui()

    def _init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # 创建滚动区域
        scroll_area = SingleDirectionScrollArea(orient=Qt.Orientation.Vertical)
        scroll_area.setWidgetResizable(True)
        scroll_area.enableTransparentBackground()
        scroll_area.setStyleSheet(
            "QScrollArea { border: none; background: transparent; }"
        )
        scroll_area.viewport().setStyleSheet("background: transparent;")

        # 创建内容容器
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(16, 16, 16, 16)
        content_layout.setSpacing(12)

        # 应用列表 - 直接创建可用的复选框，在卸载时再检查安装状态
        for app_name, package_name in self.apps_dict.items():
            checkbox = CheckBox(app_name)
            checkbox.setEnabled(True)  # 默认启用，让用户可以选择

            self.checkboxes[app_name] = checkbox
            content_layout.addWidget(checkbox)

        content_layout.addStretch()

        # 设置滚动区域内容
        scroll_area.setWidget(content_widget)
        layout.addWidget(scroll_area)

    def get_selected_apps(self) -> List[Dict[str, str]]:
        """获取选中的应用"""
        selected = []
        for app_name, checkbox in self.checkboxes.items():
            if checkbox.isChecked() and checkbox.isEnabled():
                selected.append({"name": app_name, "package": self.apps_dict[app_name]})
        return selected

    def select_all(self, checked: bool):
        """全选/取消全选"""
        for checkbox in self.checkboxes.values():
            if checkbox.isEnabled():
                checkbox.setChecked(checked)


class OneDriveTab(QWidget):
    """OneDrive清理选项卡"""

    def __init__(self):
        super().__init__()
        self.checkboxes = {}
        self._init_ui()

    def _init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # 创建滚动区域
        scroll_area = SingleDirectionScrollArea(orient=Qt.Orientation.Vertical)
        scroll_area.setWidgetResizable(True)
        scroll_area.enableTransparentBackground()
        scroll_area.setStyleSheet(
            "QScrollArea { border: none; background: transparent; }"
        )
        scroll_area.viewport().setStyleSheet("background: transparent;")

        # 创建内容容器
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(16, 16, 16, 16)
        content_layout.setSpacing(12)

        # OneDrive清理选项
        for task_name, commands in ONE_DRIVE_CLEANUP.items():
            checkbox = CheckBox(task_name)
            self.checkboxes[task_name] = checkbox
            content_layout.addWidget(checkbox)

        content_layout.addStretch()

        # 设置滚动区域内容
        scroll_area.setWidget(content_widget)
        layout.addWidget(scroll_area)

    def get_selected_tasks(self) -> List[Dict[str, str]]:
        """获取选中的任务"""
        selected = []
        for task_name, checkbox in self.checkboxes.items():
            if checkbox.isChecked():
                selected.append(
                    {
                        "name": task_name,
                        "type": "onedrive_task",
                        "commands": ONE_DRIVE_CLEANUP[task_name],
                    }
                )
        return selected

    def select_all(self, checked: bool):
        """全选/取消全选"""
        for checkbox in self.checkboxes.values():
            checkbox.setChecked(checked)


class PreinstalledView(SingleDirectionScrollArea):
    """预装应用管理视图"""

    def __init__(self):
        super().__init__(orient=Qt.Orientation.Vertical)
        self.setObjectName("PreinstalledView")  # 设置对象名称，FluentWindow要求
        self.setWidgetResizable(True)
        self.enableTransparentBackground()
        self.setStyleSheet("QScrollArea { border: none; background: transparent; }")
        self.viewport().setStyleSheet("background: transparent;")

        self.worker = None
        self._init_ui()

    def _init_ui(self):
        """初始化界面"""
        # 创建内容容器
        content_widget = QWidget()
        content_widget.setStyleSheet("background: transparent;")
        # 设置大小策略，防止内容被拉伸
        content_widget.setSizePolicy(
            QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Minimum
        )

        layout = QVBoxLayout(content_widget)
        layout.setContentsMargins(24, 24, 24, 24)
        layout.setSpacing(20)  # 统一为20px，与其他视图保持一致

        # 页面标题 - 使用TitleLabel + CaptionLabel标准格式
        title_widget = QWidget()
        title_layout = QVBoxLayout(title_widget)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(4)

        title_label = TitleLabel("预装应用管理", self)
        subtitle_label = CaptionLabel("卸载Windows预装应用，释放存储空间", self)

        title_layout.addWidget(title_label)
        title_layout.addWidget(subtitle_label)

        layout.addWidget(title_widget)

        # 主要内容区域 - 将全选按钮、选项卡和内容放在同一个卡片中
        main_card = CardWidget()
        main_layout = QVBoxLayout(main_card)
        main_layout.setContentsMargins(16, 16, 16, 16)
        main_layout.setSpacing(16)

        # 顶部控制区域
        control_layout = QHBoxLayout()
        control_layout.setSpacing(12)

        # 全选复选框
        self.select_all_checkbox = CheckBox("全选")
        self.select_all_checkbox.clicked.connect(self._on_select_all)
        control_layout.addWidget(self.select_all_checkbox)

        control_layout.addStretch()

        # 卸载按钮
        self.uninstall_btn = PushButton("卸载选中")
        self.uninstall_btn.setIcon(FluentIcon.DELETE)
        self.uninstall_btn.clicked.connect(self._uninstall_selected)
        control_layout.addWidget(self.uninstall_btn)

        main_layout.addLayout(control_layout)

        # 选项卡区域
        self.tab_widget = SegmentedWidget()
        self.stacked_widget = QStackedWidget()

        # 创建选项卡
        self.xbox_tab = AppTab("Xbox应用", APPX_PACKAGES["Windows Xbox相关应用"])
        self.store_tab = AppTab("Store应用", APPX_PACKAGES["Windows Store商店应用"])
        self.codec_tab = AppTab("编解码器", APPX_PACKAGES["Windows 音视频编解码器应用"])
        self.system_tab = AppTab("系统应用", APPX_PACKAGES["Windows 系统预装应用"])
        self.onedrive_tab = OneDriveTab()

        # 添加选项卡
        self.tab_widget.addItem(
            "xbox", "Xbox应用", lambda: self.stacked_widget.setCurrentIndex(0)
        )
        self.tab_widget.addItem(
            "store", "Store应用", lambda: self.stacked_widget.setCurrentIndex(1)
        )
        self.tab_widget.addItem(
            "codec", "编解码器", lambda: self.stacked_widget.setCurrentIndex(2)
        )
        self.tab_widget.addItem(
            "system", "系统应用", lambda: self.stacked_widget.setCurrentIndex(3)
        )
        self.tab_widget.addItem(
            "onedrive", "OneDrive", lambda: self.stacked_widget.setCurrentIndex(4)
        )

        self.stacked_widget.addWidget(self.xbox_tab)
        self.stacked_widget.addWidget(self.store_tab)
        self.stacked_widget.addWidget(self.codec_tab)
        self.stacked_widget.addWidget(self.system_tab)
        self.stacked_widget.addWidget(self.onedrive_tab)

        # 设置默认选项卡
        self.tab_widget.setCurrentItem("xbox")
        self.stacked_widget.setCurrentIndex(0)

        main_layout.addWidget(self.tab_widget)
        main_layout.addWidget(self.stacked_widget, 1)

        layout.addWidget(main_card)

        # 进度显示
        self.progress_ring = ProgressRing()
        self.progress_ring.setVisible(False)
        self.status_label = BodyLabel("准备就绪")

        progress_layout = QHBoxLayout()
        progress_layout.addWidget(self.progress_ring)
        progress_layout.addWidget(self.status_label)
        progress_layout.addStretch()

        layout.addLayout(progress_layout)

        # 设置为滚动区域的内容
        self.setWidget(content_widget)

    def _on_select_all(self, checked: bool):
        """全选/取消全选 - 对所有选项卡生效"""
        tabs = [
            self.xbox_tab,
            self.store_tab,
            self.codec_tab,
            self.system_tab,
            self.onedrive_tab,
        ]

        # 对所有选项卡都执行全选/取消全选操作
        for tab in tabs:
            tab.select_all(checked)

    def _uninstall_selected(self):
        """卸载选中的应用"""
        # 收集所有选中的应用和任务
        selected_apps = []
        selected_apps.extend(self.xbox_tab.get_selected_apps())
        selected_apps.extend(self.store_tab.get_selected_apps())
        selected_apps.extend(self.codec_tab.get_selected_apps())
        selected_apps.extend(self.system_tab.get_selected_apps())

        # OneDrive任务单独处理
        onedrive_tasks = self.onedrive_tab.get_selected_tasks()

        if not selected_apps and not onedrive_tasks:
            InfoBar.warning(
                title="无任务可执行",
                content="请至少选择一个应用或OneDrive清理任务",
                orient=Qt.Orientation.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=3000,
                parent=self,
            )
            return

        # 开始卸载（在工作线程中逐个检查应用安装状态）
        self._start_uninstall(selected_apps, onedrive_tasks)

    def _start_uninstall(
        self, selected_apps: List[Dict[str, str]], onedrive_tasks: List[Dict[str, str]]
    ):
        """开始卸载任务"""
        self.progress_ring.setVisible(True)
        self.uninstall_btn.setEnabled(False)

        # 创建工作线程，同时传递应用和OneDrive任务
        self.worker = AppManagerWorker(selected_apps, onedrive_tasks)
        self.worker.progress_updated.connect(self.progress_ring.setValue)
        self.worker.status_updated.connect(self.status_label.setText)
        self.worker.finished.connect(self._on_uninstall_finished)
        self.worker.error_occurred.connect(self._on_uninstall_error)
        self.worker.start()

    def _on_uninstall_finished(self):
        """卸载完成"""
        self.progress_ring.setVisible(False)
        self.uninstall_btn.setEnabled(True)
        self.status_label.setText("卸载完成")

        InfoBar.success(
            title="卸载完成",
            content="应用卸载已完成",
            orient=Qt.Orientation.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=3000,
            parent=self,
        )

    def _on_uninstall_error(self, error: str):
        """卸载错误"""
        self.progress_ring.setVisible(False)
        self.uninstall_btn.setEnabled(True)
        self.status_label.setText(f"卸载失败: {error}")

        InfoBar.error(
            title="卸载失败",
            content=f"应用卸载过程中发生错误: {error}",
            orient=Qt.Orientation.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=5000,
            parent=self,
        )
