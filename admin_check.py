# -*- coding: utf-8 -*-
"""
管理员权限检查模块
"""
import ctypes
import sys
import os
from PySide6.QtWidgets import QMessageBox


def is_admin():
    """检查当前程序是否以管理员权限运行"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False


def run_as_admin():
    """以管理员权限重新启动程序"""
    if is_admin():
        return True
    else:
        try:
            # 获取当前脚本路径
            script = os.path.abspath(sys.argv[0])
            params = ' '.join([f'"{arg}"' for arg in sys.argv[1:]])
            
            # 使用ShellExecute以管理员权限运行
            ctypes.windll.shell32.ShellExecuteW(
                None, 
                "runas", 
                sys.executable, 
                f'"{script}" {params}', 
                None, 
                1
            )
            return False
        except Exception as e:
            print(f"无法以管理员权限启动: {e}")
            return False


def check_admin_permission(parent=None):
    """
    检查管理员权限，如果没有则提示用户
    
    Args:
        parent: 父窗口，用于显示对话框
        
    Returns:
        bool: 是否有管理员权限
    """
    if not is_admin():
        msg = QMessageBox(parent)
        msg.setWindowTitle("需要管理员权限")
        msg.setText("此应用程序需要管理员权限才能正常运行。")
        msg.setInformativeText("请以管理员身份重新启动应用程序。")
        msg.setIcon(QMessageBox.Icon.Warning)
        msg.setStandardButtons(QMessageBox.StandardButton.Ok)
        msg.exec()
        return False
    return True


def require_admin():
    """
    装饰器：要求函数必须在管理员权限下运行
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            if not is_admin():
                raise PermissionError("此操作需要管理员权限")
            return func(*args, **kwargs)
        return wrapper
    return decorator
