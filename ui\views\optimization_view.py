# -*- coding: utf-8 -*-
"""
优化清理视图模块 - 严格按照需求文档实现
"""
import subprocess
import os
import time
from typing import Dict, List, Any
from PySide6.QtWidgets import (
    QFrame,
    QVBoxLayout,
    QHBoxLayout,
    QWidget,
    QStackedWidget,
    QScrollArea,
    QApplication,
    QSizePolicy,
)
from PySide6.QtCore import Qt, QThread, Signal
from qfluentwidgets import (
    HeaderCardWidget,
    CardWidget,
    SegmentedWidget,
    PushButton,
    CheckBox,
    ProgressBar,
    BodyLabel,
    TitleLabel,
    CaptionLabel,
    InfoBar,
    InfoBarPosition,
    FluentIcon,
    SingleDirectionScrollArea,
)

from config.powershell_commands import EXECUTION_POLICY_COMMAND, UNLOCK_POWER_COMMAND
from config.registry_commands import REGISTRY_COMMANDS
from config.system_cleanup import SYSTEM_CLEANUP


class OptimizationWorker(QThread):
    """优化清理工作线程"""

    progress_updated = Signal(int)
    status_updated = Signal(str)
    finished = Signal()
    error_occurred = Signal(str)

    def __init__(self, selected_tasks: Dict[str, List[Dict[str, str]]]):
        super().__init__()
        self.selected_tasks = selected_tasks

    def run(self):
        """执行优化清理任务"""
        try:
            total_tasks = sum(len(tasks) for tasks in self.selected_tasks.values())
            completed = 0

            for category, tasks in self.selected_tasks.items():
                for task in tasks:
                    self.status_updated.emit(f"正在执行: {task['name']}")

                    if category == "powershell":
                        self._execute_powershell(task["command"])
                    elif category == "registry":
                        self._execute_registry(task["command"])
                    elif category == "cleanup":
                        self._execute_cleanup(task["command"])

                    completed += 1
                    progress = int((completed / total_tasks) * 100)
                    self.progress_updated.emit(progress)
                    time.sleep(0.1)

            # 执行后操作：重启文件资源管理器 + 删除 iconcache.db
            self.status_updated.emit("正在重启文件资源管理器...")
            subprocess.run(
                "taskkill /f /im explorer.exe", shell=True, capture_output=True
            )
            time.sleep(1)
            subprocess.run("start explorer.exe", shell=True)

            # 删除图标缓存
            iconcache_path = os.path.expandvars("%LOCALAPPDATA%\\IconCache.db")
            if os.path.exists(iconcache_path):
                try:
                    os.remove(iconcache_path)
                    self.status_updated.emit("已删除图标缓存文件")
                except:
                    pass

            self.status_updated.emit("优化清理完成")
            self.finished.emit()

        except Exception as e:
            self.error_occurred.emit(str(e))

    def _execute_powershell(self, command: str):
        """执行PowerShell命令"""
        subprocess.run(
            f'powershell -Command "{command}"', shell=True, capture_output=True
        )

    def _execute_registry(self, commands: List[str]):
        """执行注册表命令"""
        for cmd in commands:
            subprocess.run(cmd, shell=True, capture_output=True)

    def _execute_cleanup(self, commands: List[str]):
        """执行系统清理命令"""
        for cmd in commands:
            subprocess.run(cmd, shell=True, capture_output=True)


class PowerShellTab(QWidget):
    """PowerShell设置选项卡"""

    def __init__(self):
        super().__init__()
        self.checkboxes = {}
        self._init_ui()

    def _init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(12)

        # PowerShell选项
        powershell_options = [
            ("execution_policy", "设置执行策略为Bypass", "允许运行所有PowerShell脚本"),
            ("unlock_power", "解锁电源高级选项", "显示更多电源管理选项"),
        ]

        for key, title, description in powershell_options:
            checkbox = CheckBox(title)
            self.checkboxes[key] = checkbox
            layout.addWidget(checkbox)

        layout.addStretch()

    def get_selected_tasks(self) -> List[Dict[str, str]]:
        """获取选中的任务"""
        tasks = []
        if self.checkboxes["execution_policy"].isChecked():
            tasks.append(
                {"name": "设置执行策略为Bypass", "command": EXECUTION_POLICY_COMMAND}
            )
        if self.checkboxes["unlock_power"].isChecked():
            tasks.append({"name": "解锁电源高级选项", "command": UNLOCK_POWER_COMMAND})
        return tasks

    def select_all(self, checked: bool):
        """全选/取消全选"""
        for checkbox in self.checkboxes.values():
            checkbox.setChecked(checked)


class RegistryTab(QWidget):
    """注册表优化选项卡"""

    def __init__(self):
        super().__init__()
        self.checkboxes = {}
        self._init_ui()

    def _init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(12)

        # 创建滚动区域
        scroll_area = SingleDirectionScrollArea(orient=Qt.Orientation.Vertical)
        scroll_area.setWidgetResizable(True)
        scroll_area.enableTransparentBackground()
        # 设置透明背景，与其他选项卡保持一致
        scroll_area.setStyleSheet("QScrollArea { background: transparent; }")
        scroll_area.viewport().setStyleSheet("background: transparent;")

        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        scroll_layout.setSpacing(8)

        # 按分类添加注册表选项
        for category, items in REGISTRY_COMMANDS.items():
            # 分类标题
            category_label = BodyLabel(category)
            category_label.setStyleSheet("font-weight: bold;")
            scroll_layout.addWidget(category_label)

            # 分类选项
            for item_name, commands in items.items():
                checkbox = CheckBox(item_name)
                self.checkboxes[f"{category}_{item_name}"] = checkbox
                scroll_layout.addWidget(checkbox)

            scroll_layout.addSpacing(8)

        scroll_area.setWidget(scroll_widget)
        layout.addWidget(scroll_area)

    def get_selected_tasks(self) -> List[Dict[str, str]]:
        """获取选中的任务"""
        tasks = []
        for key, checkbox in self.checkboxes.items():
            if checkbox.isChecked():
                category, item_name = key.split("_", 1)
                commands = REGISTRY_COMMANDS[category][item_name]
                if isinstance(commands, list):
                    for cmd in commands:
                        tasks.append(
                            {"name": f"{category} - {item_name}", "command": cmd}
                        )
                else:
                    tasks.append(
                        {"name": f"{category} - {item_name}", "command": commands}
                    )
        return tasks

    def select_all(self, checked: bool):
        """全选/取消全选"""
        for checkbox in self.checkboxes.values():
            checkbox.setChecked(checked)


class SystemCleanupTab(QWidget):
    """系统清理选项卡"""

    def __init__(self):
        super().__init__()
        self.checkboxes = {}
        self._init_ui()

    def _init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(12)

        # 系统清理选项
        for task_name, commands in SYSTEM_CLEANUP.items():
            checkbox = CheckBox(task_name)
            self.checkboxes[task_name] = checkbox
            layout.addWidget(checkbox)

        layout.addStretch()

    def get_selected_tasks(self) -> List[Dict[str, str]]:
        """获取选中的任务"""
        tasks = []
        for task_name, checkbox in self.checkboxes.items():
            if checkbox.isChecked():
                commands = SYSTEM_CLEANUP[task_name]
                for cmd in commands:
                    tasks.append({"name": task_name, "command": cmd})
        return tasks

    def select_all(self, checked: bool):
        """全选/取消全选"""
        for checkbox in self.checkboxes.values():
            checkbox.setChecked(checked)


class OptimizationView(SingleDirectionScrollArea):
    """系统优化清理视图"""

    def __init__(self, parent=None):
        super().__init__(orient=Qt.Orientation.Vertical, parent=parent)
        self.setObjectName("OptimizationView")  # 设置对象名称，FluentWindow要求
        self.setWidgetResizable(True)
        self.enableTransparentBackground()
        self.setStyleSheet("QScrollArea { border: none; background: transparent; }")
        self.viewport().setStyleSheet("background: transparent;")

        self.worker = None
        self._init_ui()

    def _init_ui(self):
        """初始化界面"""
        # 创建内容容器
        content_widget = QWidget()
        content_widget.setStyleSheet("background: transparent;")
        # 设置大小策略，防止内容被拉伸
        content_widget.setSizePolicy(
            QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Minimum
        )

        layout = QVBoxLayout(content_widget)
        layout.setContentsMargins(24, 24, 24, 24)
        layout.setSpacing(20)  # 统一为20px，与其他视图保持一致

        # 页面标题 - 使用TitleLabel + CaptionLabel标准格式
        title_widget = QWidget()
        title_layout = QVBoxLayout(title_widget)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(4)

        title_label = TitleLabel("系统优化清理", self)
        subtitle_label = CaptionLabel("优化系统设置，清理垃圾文件，提升性能", self)

        title_layout.addWidget(title_label)
        title_layout.addWidget(subtitle_label)

        layout.addWidget(title_widget)

        # 全局控制区域
        control_card = CardWidget()
        control_layout = QHBoxLayout(control_card)
        control_layout.setContentsMargins(16, 16, 16, 16)  # 统一边距为16px

        # 全选复选框 - 简化文本
        self.select_all_checkbox = CheckBox("全选")
        self.select_all_checkbox.stateChanged.connect(self._on_select_all_changed)
        control_layout.addWidget(self.select_all_checkbox)

        control_layout.addStretch()

        # 执行按钮
        self.execute_all_btn = PushButton("一键执行")
        self.execute_all_btn.setIcon(FluentIcon.SPEED_HIGH)
        self.execute_selected_btn = PushButton("执行选中")
        self.execute_selected_btn.setIcon(FluentIcon.PLAY)

        self.execute_all_btn.clicked.connect(self._execute_all)
        self.execute_selected_btn.clicked.connect(self._execute_selected)

        control_layout.addWidget(self.execute_all_btn)
        control_layout.addWidget(self.execute_selected_btn)

        # 将全选按钮移到选项卡卡片中
        control_card.setVisible(False)  # 隐藏原来的控制卡片

        # 选项卡区域 - 包含全选按钮、选项卡和内容
        tab_card = CardWidget()
        tab_layout = QVBoxLayout(tab_card)
        tab_layout.setContentsMargins(16, 16, 16, 16)
        tab_layout.setSpacing(16)

        # 顶部控制区域（移到选项卡卡片中）
        top_control_layout = QHBoxLayout()
        top_control_layout.setSpacing(12)

        # 重新创建全选复选框
        self.select_all_checkbox = CheckBox("全选")
        self.select_all_checkbox.stateChanged.connect(self._on_select_all_changed)
        top_control_layout.addWidget(self.select_all_checkbox)

        top_control_layout.addStretch()

        # 重新创建执行按钮
        self.execute_all_btn = PushButton("一键执行")
        self.execute_all_btn.setIcon(FluentIcon.SPEED_HIGH)
        self.execute_selected_btn = PushButton("执行选中")
        self.execute_selected_btn.setIcon(FluentIcon.PLAY)
        self.execute_all_btn.clicked.connect(self._execute_all)
        self.execute_selected_btn.clicked.connect(self._execute_selected)

        top_control_layout.addWidget(self.execute_all_btn)
        top_control_layout.addWidget(self.execute_selected_btn)

        tab_layout.addLayout(top_control_layout)

        # 选项卡控件
        self.tab_widget = SegmentedWidget()
        self.stacked_widget = QStackedWidget()

        # 创建选项卡
        self.powershell_tab = PowerShellTab()
        self.registry_tab = RegistryTab()
        self.cleanup_tab = SystemCleanupTab()

        # 添加选项卡
        self.tab_widget.addItem(
            "powershell",
            "PowerShell设置",
            lambda: self.stacked_widget.setCurrentIndex(0),
        )
        self.tab_widget.addItem(
            "registry", "注册表优化", lambda: self.stacked_widget.setCurrentIndex(1)
        )
        self.tab_widget.addItem(
            "cleanup", "系统清理", lambda: self.stacked_widget.setCurrentIndex(2)
        )

        self.stacked_widget.addWidget(self.powershell_tab)
        self.stacked_widget.addWidget(self.registry_tab)
        self.stacked_widget.addWidget(self.cleanup_tab)

        # 设置默认选项卡
        self.tab_widget.setCurrentItem("powershell")
        self.stacked_widget.setCurrentIndex(0)

        tab_layout.addWidget(self.tab_widget)
        tab_layout.addWidget(self.stacked_widget, 1)

        layout.addWidget(tab_card)

        # 执行进度区域
        self.progress_bar = ProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        self.status_label = BodyLabel("就绪")
        layout.addWidget(self.status_label)

        # 设置为滚动区域的内容
        self.setWidget(content_widget)

    def _on_select_all_changed(self, checked: bool):
        """全选状态改变"""
        self.powershell_tab.select_all(checked)
        self.registry_tab.select_all(checked)
        self.cleanup_tab.select_all(checked)

    def _execute_all(self):
        """一键执行所有任务"""
        self.select_all_checkbox.setChecked(True)
        self._execute_selected()

    def _execute_selected(self):
        """执行选中的任务"""
        # 收集选中的任务
        selected_tasks = {
            "powershell": self.powershell_tab.get_selected_tasks(),
            "registry": self.registry_tab.get_selected_tasks(),
            "cleanup": self.cleanup_tab.get_selected_tasks(),
        }

        # 检查是否有选中的任务
        total_tasks = sum(len(tasks) for tasks in selected_tasks.values())
        if total_tasks == 0:
            InfoBar.warning(
                title="无任务可执行",
                content="请至少选择一个优化选项",
                orient=Qt.Orientation.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=3000,
                parent=self,
            )
            return

        # 开始执行
        self._start_execution(selected_tasks)

    def _start_execution(self, selected_tasks: Dict[str, List[str]]):
        """开始执行任务"""
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.execute_all_btn.setEnabled(False)
        self.execute_selected_btn.setEnabled(False)

        # 创建工作线程
        self.worker = OptimizationWorker(selected_tasks)
        self.worker.progress_updated.connect(self.progress_bar.setValue)
        self.worker.status_updated.connect(self.status_label.setText)
        self.worker.finished.connect(self._on_execution_finished)
        self.worker.error_occurred.connect(self._on_execution_error)
        self.worker.start()

    def _on_execution_finished(self):
        """执行完成"""
        self.progress_bar.setVisible(False)
        self.execute_all_btn.setEnabled(True)
        self.execute_selected_btn.setEnabled(True)
        self.status_label.setText("优化清理完成")

        InfoBar.success(
            title="执行完成",
            content="系统优化清理已完成",
            orient=Qt.Orientation.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=3000,
            parent=self,
        )

    def _on_execution_error(self, error: str):
        """执行错误"""
        self.progress_bar.setVisible(False)
        self.execute_all_btn.setEnabled(True)
        self.execute_selected_btn.setEnabled(True)
        self.status_label.setText(f"执行失败: {error}")

        InfoBar.error(
            title="执行失败",
            content=f"优化清理过程中发生错误: {error}",
            orient=Qt.Orientation.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=5000,
            parent=self,
        )
