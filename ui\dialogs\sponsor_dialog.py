#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
赞助对话框
显示支付宝和微信支付二维码
"""

import os
from PySide6.QtCore import Qt
from PySide6.QtWidgets import QVBoxLayout, QHBoxLayout, QLabel
from PySide6.QtGui import QPixmap

from qfluentwidgets import (
    MessageBoxBase,
    BodyLabel,
    SubtitleLabel,
    SegmentedWidget,
)


class SponsorDialog(MessageBoxBase):
    """赞助对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)

        # 设置对话框标题
        self.titleLabel = SubtitleLabel("💖 赞助作者")

        # 感谢文本
        self.thanksLabel = BodyLabel(
            "感谢您对项目的支持！\n您的赞助将帮助项目持续发展。"
        )
        self.thanksLabel.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.thanksLabel.setWordWrap(True)

        # 创建选项卡
        self.segmentedWidget = SegmentedWidget()
        self.segmentedWidget.addItem("alipay", "支付宝", self._show_alipay)
        self.segmentedWidget.addItem("wechat", "微信支付", self._show_wechat)
        self.segmentedWidget.setCurrentItem("alipay")

        # 二维码显示区域
        self.qr_label = QLabel()
        self.qr_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.qr_label.setFixedSize(200, 200)
        self.qr_label.setStyleSheet(
            """
            QLabel {
                border: 2px solid #E0E0E0;
                border-radius: 8px;
                background-color: white;
            }
        """
        )

        # 提示文本
        self.tip_label = BodyLabel("支付宝扫码赞助")
        self.tip_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.tip_label.setWordWrap(True)

        # 添加组件到布局
        self.viewLayout.addWidget(self.titleLabel)
        self.viewLayout.addWidget(self.thanksLabel)
        self.viewLayout.addWidget(self.segmentedWidget)

        # 将二维码标签居中添加
        self.viewLayout.addWidget(self.qr_label, 0, Qt.AlignmentFlag.AlignHCenter)
        self.viewLayout.addWidget(self.tip_label)

        # 设置对话框最小宽度
        self.widget.setMinimumWidth(350)

        # 连接选项卡切换信号
        self.segmentedWidget.currentItemChanged.connect(self._on_tab_changed)

        # 默认显示支付宝
        self._show_alipay()

        # 隐藏确认按钮，只保留关闭按钮
        self.yesButton.hide()
        self.cancelButton.setText("关闭")

    def _show_alipay(self):
        """显示支付宝二维码"""
        self._load_qr_image("assets/alipay.png", "支付宝扫码赞助")

    def _show_wechat(self):
        """显示微信二维码"""
        self._load_qr_image("assets/wechat.png", "微信扫码赞助")

    def _on_tab_changed(self, routeKey):
        """选项卡切换事件"""
        if routeKey == "alipay":
            self._show_alipay()
        elif routeKey == "wechat":
            self._show_wechat()

    def _load_qr_image(self, image_path: str, tip_text: str):
        """加载二维码图片"""
        if os.path.exists(image_path):
            pixmap = QPixmap(image_path)
            if not pixmap.isNull():
                # 缩放图片以适应标签大小
                scaled_pixmap = pixmap.scaled(
                    180,
                    180,
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation,
                )
                self.qr_label.setPixmap(scaled_pixmap)
            else:
                self.qr_label.setText("❌\n图片加载失败")
        else:
            self.qr_label.setText("❌\n二维码图片不存在")

        # 更新提示文本
        self.tip_label.setText(tip_text)
