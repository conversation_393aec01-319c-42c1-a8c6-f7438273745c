#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
设置关于视图
提供应用设置、更新检查、赞助支持等功能
"""

import os
from PySide6.QtCore import Qt
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QSizePolicy
from PySide6.QtGui import QPixmap

from qfluentwidgets import (
    SettingCardGroup,
    SwitchSettingCard,
    OptionsSettingCard,
    PushSettingCard,
    HeaderCardWidget,
    ElevatedCardWidget,
    BodyLabel,
    TitleLabel,
    CaptionLabel,
    InfoBar,
    InfoBarPosition,
    MessageBox,
    FluentIcon,
    SwitchButton,
    HyperlinkButton,
    SingleDirectionScrollArea,
)

from core.updater import get_settings_manager, UpdateChecker
from ui.dialogs.sponsor_dialog import SponsorDialog


class SettingsView(SingleDirectionScrollArea):
    """设置关于视图"""

    def __init__(self):
        super().__init__(orient=Qt.Orientation.Vertical)
        self.setObjectName("SettingsView")  # 设置对象名称，FluentWindow要求
        self.setWidgetResizable(True)
        self.enableTransparentBackground()
        self.setStyleSheet("QScrollArea { border: none; background: transparent; }")
        self.viewport().setStyleSheet("background: transparent;")

        self.settings_manager = get_settings_manager()
        self.app_info = self.settings_manager.get_app_info()
        self._init_ui()

    def _init_ui(self):
        """初始化界面"""
        # 创建内容容器
        content_widget = QWidget()
        content_widget.setStyleSheet("background: transparent;")
        # 设置大小策略，防止内容被拉伸
        content_widget.setSizePolicy(
            QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Minimum
        )

        layout = QVBoxLayout(content_widget)
        layout.setContentsMargins(24, 24, 24, 24)
        layout.setSpacing(20)  # 固定间距

        # 页面标题 - 使用TitleLabel + CaptionLabel标准格式
        title_widget = QWidget()
        title_layout = QVBoxLayout(title_widget)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(4)

        title_label = TitleLabel("设置关于", self)
        subtitle_label = CaptionLabel("应用程序设置和相关信息", self)

        title_layout.addWidget(title_label)
        title_layout.addWidget(subtitle_label)

        layout.addWidget(title_widget)

        # 添加内容
        self._create_app_info_card(layout)
        self._create_app_settings_card(layout)
        self._create_support_card(layout)

        # 添加固定的底部空间
        layout.addSpacing(30)

        # 设置为滚动区域的内容
        self.setWidget(content_widget)

    def _create_app_settings_card(self, parent_layout):
        """创建应用设置卡片"""
        # 创建设置卡片组
        settings_group = SettingCardGroup("应用设置")

        # 检查更新设置卡片
        self.update_card = PushSettingCard(
            "检查更新", FluentIcon.UPDATE, "检查是否有新版本可用", "检查更新"
        )
        self.update_card.button.clicked.connect(self._check_for_updates)
        settings_group.addSettingCard(self.update_card)

        parent_layout.addWidget(settings_group)

    def _create_app_info_card(self, parent_layout):
        """创建应用信息卡片"""
        info_card = HeaderCardWidget()
        info_card.setTitle("设置关于")
        info_card.setFixedHeight(280)  # 增加高度以容纳所有内容

        # 应用信息布局
        info_layout = QVBoxLayout()
        info_layout.setSpacing(12)
        info_layout.setContentsMargins(0, 16, 0, 0)

        # 应用图标和基本信息 - 改为居中的垂直布局
        app_info_layout = QVBoxLayout()
        app_info_layout.setSpacing(16)
        app_info_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 应用图标
        icon_label = QLabel()
        icon_path = "assets/icon.ico"
        if os.path.exists(icon_path):
            pixmap = QPixmap(icon_path)
            if not pixmap.isNull():
                scaled_pixmap = pixmap.scaled(
                    80,
                    80,
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation,
                )
                icon_label.setPixmap(scaled_pixmap)
            else:
                icon_label.setText("图标")
                icon_label.setStyleSheet("font-size: 16px; color: #666;")
        else:
            icon_label.setText("图标")
            icon_label.setStyleSheet("font-size: 16px; color: #666;")

        icon_label.setFixedSize(80, 80)
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        app_info_layout.addWidget(icon_label, 0, Qt.AlignmentFlag.AlignCenter)

        # 应用信息文本 - 居中对齐
        text_layout = QVBoxLayout()
        text_layout.setSpacing(8)
        text_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 应用名称
        name_label = TitleLabel(self.app_info["name"])
        name_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        text_layout.addWidget(name_label)

        # 版本号
        version_label = BodyLabel(f"版本 V{self.app_info['version']}")
        version_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        text_layout.addWidget(version_label)

        # 版权信息
        copyright_label = CaptionLabel(self.app_info["copyright"])
        copyright_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        text_layout.addWidget(copyright_label)

        app_info_layout.addLayout(text_layout)

        info_layout.addLayout(app_info_layout)

        # 描述信息 - 居中显示
        desc_label = BodyLabel(self.app_info["description"])
        desc_label.setWordWrap(True)
        desc_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        info_layout.addWidget(desc_label)

        # 添加到卡片
        info_widget = QWidget()
        info_widget.setLayout(info_layout)
        info_card.viewLayout.addWidget(info_widget)

        parent_layout.addWidget(info_card)

    def _create_support_card(self, parent_layout):
        """创建支持作者卡片"""
        # 创建支持作者卡片组
        support_group = SettingCardGroup("支持作者")

        # 赞助作者卡片
        sponsor_card = PushSettingCard(
            "赞助作者", FluentIcon.HEART, "支持项目持续发展", "查看二维码"
        )
        sponsor_card.button.clicked.connect(self._show_sponsor_dialog)
        support_group.addSettingCard(sponsor_card)

        # 抖音主页卡片
        douyin_card = PushSettingCard(
            "抖音主页", FluentIcon.PHONE, "关注作者获取最新动态", "访问主页"
        )
        douyin_card.button.clicked.connect(
            lambda: self._open_url(self.app_info["douyin_url"])
        )
        support_group.addSettingCard(douyin_card)

        # QQ群卡片
        qq_card = PushSettingCard(
            "官方Q群", FluentIcon.PEOPLE, "加入交流群获取技术支持", "加入群聊"
        )
        qq_card.button.clicked.connect(
            lambda: self._open_url(self.app_info["qq_group_url"])
        )
        support_group.addSettingCard(qq_card)

        parent_layout.addWidget(support_group)

    def _open_url(self, url: str):
        """打开URL链接"""
        import webbrowser

        webbrowser.open(url)



    def _check_for_updates(self):
        """检查更新"""
        # 禁用按钮，防止重复点击
        self.update_card.button.setEnabled(False)
        self.update_card.button.setText("检查中...")

        # 显示检查状态
        InfoBar.info(
            title="正在检查更新",
            content="正在连接服务器检查最新版本...",
            orient=Qt.Orientation.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=3000,
            parent=self.window(),
        )

        # 创建更新检查器
        self.update_checker = self.settings_manager.create_update_checker()
        self.update_checker.update_checked.connect(self._on_update_checked)
        self.update_checker.check_error.connect(self._on_update_error)
        self.update_checker.start()

    def _on_update_checked(self, has_update: bool, update_info: dict):
        """更新检查完成处理"""
        # 恢复按钮状态
        self.update_card.button.setEnabled(True)
        self.update_card.button.setText("检查更新")

        if has_update:
            # 有新版本
            version = update_info.get("version", "未知")
            changelog = update_info.get("changelog", [])
            download_url = update_info.get("download_url", "")

            changelog_text = "\n".join([f"• {item}" for item in changelog[:5]])
            if len(changelog) > 5:
                changelog_text += f"\n... 还有 {len(changelog) - 5} 项更新"

            content = f"发现新版本 V{version}\n\n更新内容:\n{changelog_text}"

            msg_box = MessageBox("发现新版本", content, self.window())

            if msg_box.exec():
                # 用户确认更新，打开下载页面
                try:
                    import webbrowser

                    webbrowser.open(download_url)
                except Exception as e:
                    InfoBar.error(
                        title="打开失败",
                        content=f"无法打开下载页面: {str(e)}",
                        orient=Qt.Orientation.Horizontal,
                        isClosable=True,
                        position=InfoBarPosition.TOP,
                        duration=3000,
                        parent=self.window(),
                    )
        else:
            # 已是最新版本
            InfoBar.success(
                title="已是最新版本",
                content="当前版本已是最新版本",
                orient=Qt.Orientation.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=2000,
                parent=self.window(),
            )

    def _on_update_error(self, error_message: str):
        """更新检查错误处理"""
        # 恢复按钮状态
        self.update_card.button.setEnabled(True)
        self.update_card.button.setText("检查更新")

        InfoBar.error(
            title="检查更新失败",
            content=error_message,
            orient=Qt.Orientation.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=3000,
            parent=self.window(),
        )

    def _show_sponsor_dialog(self):
        """显示赞助对话框"""
        dialog = SponsorDialog(self.window())
        dialog.exec()
