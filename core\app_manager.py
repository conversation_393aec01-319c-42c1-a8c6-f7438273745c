# -*- coding: utf-8 -*-
"""
应用管理服务模块
"""
import subprocess
import re
from typing import List, Dict, Tuple
from PySide6.QtCore import QThread, Signal


class AppManagerWorker(QThread):
    """应用卸载工作线程"""

    progress_updated = Signal(int)
    status_updated = Signal(str)
    app_uninstalled = Signal(str, bool)  # app_name, success
    finished = Signal()
    error_occurred = Signal(str)

    def __init__(
        self,
        selected_apps: List[Dict[str, str]],
        onedrive_tasks: List[Dict[str, str]] = None,
    ):
        super().__init__()
        self.selected_apps = selected_apps
        self.onedrive_tasks = onedrive_tasks or []

    def run(self):
        """执行应用卸载和OneDrive清理任务"""
        try:
            total_tasks = len(self.selected_apps) + len(self.onedrive_tasks)
            completed = 0

            # 处理应用卸载
            for app in self.selected_apps:
                app_name = app["name"]
                package_name = app["package"]

                # 先检查应用是否已安装
                self.status_updated.emit(f"检查应用: {app_name}")

                if not AppManager.check_app_installed(package_name):
                    self.status_updated.emit(f"跳过未安装应用: {app_name}")
                    self.app_uninstalled.emit(app_name, False)  # 标记为失败（未安装）
                else:
                    # 应用已安装，执行卸载
                    self.status_updated.emit(f"正在卸载: {app_name}")
                    success = self._uninstall_uwp_app(package_name)
                    self.app_uninstalled.emit(app_name, success)

                completed += 1
                if total_tasks > 0:
                    progress = int((completed / total_tasks) * 100)
                    self.progress_updated.emit(progress)

            # 处理OneDrive清理任务
            for task in self.onedrive_tasks:
                task_name = task["name"]
                commands = task["commands"]

                self.status_updated.emit(f"执行OneDrive任务: {task_name}")

                success = self._execute_onedrive_commands(commands)
                self.app_uninstalled.emit(task_name, success)

                completed += 1
                if total_tasks > 0:
                    progress = int((completed / total_tasks) * 100)
                    self.progress_updated.emit(progress)

            self.status_updated.emit("所有任务完成")
            self.finished.emit()

        except Exception as e:
            self.error_occurred.emit(str(e))

    def _execute_onedrive_commands(self, commands: list) -> bool:
        """执行OneDrive清理命令列表"""
        try:
            success_count = 0
            total_commands = len(commands)

            for cmd in commands:
                try:
                    result = subprocess.run(
                        cmd, shell=True, capture_output=True, text=True
                    )
                    if result.returncode == 0:
                        success_count += 1
                except Exception:
                    # 某些命令可能会失败（比如删除不存在的注册表项），这是正常的
                    pass

            # 如果至少有一半的命令成功执行，就认为任务成功
            return success_count > 0 or total_commands == 0
        except Exception:
            return False

    def _uninstall_uwp_app(self, package_name: str) -> bool:
        """卸载UWP应用"""
        try:
            # 使用PowerShell命令卸载UWP应用
            cmd = f'powershell -command "Get-AppxPackage *{package_name}* | Remove-AppxPackage"'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            return result.returncode == 0
        except Exception:
            return False


class AppManager:
    """应用管理服务"""

    @staticmethod
    def get_installed_apps() -> List[Dict[str, str]]:
        """获取已安装的UWP应用列表"""
        try:
            cmd = 'powershell -command "Get-AppxPackage | Select-Object Name, PackageFullName | ConvertTo-Json"'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

            if result.returncode == 0:
                import json

                apps_data = json.loads(result.stdout)

                installed_apps = []
                for app in apps_data:
                    installed_apps.append(
                        {
                            "name": app.get("Name", ""),
                            "package_full_name": app.get("PackageFullName", ""),
                        }
                    )

                return installed_apps
            else:
                return []

        except Exception:
            return []

    @staticmethod
    def check_app_installed(package_name: str) -> bool:
        """检查应用是否已安装"""
        try:
            cmd = f'powershell -command "Get-AppxPackage *{package_name}*"'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            return result.returncode == 0 and result.stdout.strip() != ""
        except Exception:
            return False

    @staticmethod
    def execute_onedrive_cleanup(selected_tasks: List[Dict[str, str]]) -> bool:
        """执行OneDrive清理任务"""
        try:
            for task in selected_tasks:
                commands = task["commands"]
                for cmd in commands:
                    subprocess.run(cmd, shell=True, capture_output=True)
            return True
        except Exception:
            return False
