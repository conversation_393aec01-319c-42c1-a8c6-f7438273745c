#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
超频工具扫描服务
提供超频工具的扫描、检测和管理功能
"""

import os
import subprocess
import sys
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from PySide6.QtCore import QThread, Signal, QObject
from PySide6.QtGui import QIcon, QPixmap
from PySide6.QtWidgets import QFileIconProvider
from PySide6.QtCore import QFileInfo, QSize


class OCToolsScanner(QObject):
    """超频工具扫描器"""

    def __init__(self):
        super().__init__()
        self.oc_tools_dir = Path("OCTools")
        self.icon_provider = QFileIconProvider()
        self._ensure_oc_tools_dir()

    def _ensure_oc_tools_dir(self):
        """确保OCTools目录存在"""
        if not self.oc_tools_dir.exists():
            self.oc_tools_dir.mkdir(exist_ok=True)
            print(f"创建OCTools目录: {self.oc_tools_dir.absolute()}")

    def scan_tools(self) -> List[Dict[str, str]]:
        """扫描OCTools目录中的工具

        Returns:
            List[Dict]: 工具信息列表，每个字典包含name, path, icon_path等信息
        """
        tools = []

        if not self.oc_tools_dir.exists():
            return tools

        # 遍历OCTools目录下的所有子文件夹
        for tool_dir in self.oc_tools_dir.iterdir():
            if tool_dir.is_dir():
                tool_info = self._scan_tool_directory(tool_dir)
                if tool_info:
                    tools.append(tool_info)

        return tools

    def _scan_tool_directory(self, tool_dir: Path) -> Optional[Dict[str, str]]:
        """扫描单个工具目录

        Args:
            tool_dir: 工具目录路径

        Returns:
            Dict: 工具信息字典，如果没有找到可执行文件则返回None
        """
        tool_name = tool_dir.name
        exe_path = self._find_executable(tool_dir, tool_name)

        if not exe_path:
            return None

        return {
            "name": tool_name,
            "path": str(exe_path),
            "directory": str(tool_dir),
            "is_gui": self._is_gui_application(exe_path),
            "icon": self._get_file_icon(exe_path),
        }

    def _find_executable(self, tool_dir: Path, tool_name: str) -> Optional[Path]:
        """在工具目录中查找可执行文件

        Args:
            tool_dir: 工具目录
            tool_name: 工具名称

        Returns:
            Path: 可执行文件路径，如果没找到返回None
        """
        # 优先查找与文件夹同名的可执行文件
        preferred_exe = tool_dir / f"{tool_name}.exe"
        if preferred_exe.exists():
            return preferred_exe

        # 查找其他可执行文件
        for file_path in tool_dir.rglob("*.exe"):
            if file_path.is_file():
                return file_path

        return None

    def _is_gui_application(self, exe_path: Path) -> bool:
        """检查可执行文件是否为GUI应用程序

        Args:
            exe_path: 可执行文件路径

        Returns:
            bool: 如果是GUI应用程序返回True，否则返回False
        """
        try:
            # 使用file命令检查PE文件类型（在Windows上可能不可用）
            # 这里使用简单的启发式方法
            # 大多数超频工具都是GUI应用程序
            return True
        except Exception:
            return True

    def _get_file_icon(self, file_path: Path) -> QIcon:
        """获取文件图标

        Args:
            file_path: 文件路径

        Returns:
            QIcon: 文件图标
        """
        if not file_path.exists():
            return QIcon()

        file_info = QFileInfo(str(file_path))
        return self.icon_provider.icon(file_info)

    def launch_tool(self, tool_path: str, is_gui: bool = True) -> bool:
        """启动超频工具

        Args:
            tool_path: 工具路径
            is_gui: 是否为GUI应用程序

        Returns:
            bool: 启动成功返回True，否则返回False
        """
        try:
            if not os.path.exists(tool_path):
                print(f"工具不存在: {tool_path}")
                return False

            # 以管理员权限启动
            if sys.platform == "win32":
                # Windows下使用runas启动
                subprocess.Popen(
                    [
                        "powershell",
                        "-Command",
                        f"Start-Process '{tool_path}' -Verb RunAs",
                    ],
                    shell=True,
                )
            else:
                # 其他平台直接启动
                subprocess.Popen([tool_path])

            return True
        except Exception as e:
            print(f"启动工具失败: {e}")
            return False

    def open_tools_directory(self):
        """打开OCTools目录"""
        try:
            if sys.platform == "win32":
                os.startfile(str(self.oc_tools_dir.absolute()))
            elif sys.platform == "darwin":
                subprocess.run(["open", str(self.oc_tools_dir.absolute())])
            else:
                subprocess.run(["xdg-open", str(self.oc_tools_dir.absolute())])
        except Exception as e:
            print(f"打开目录失败: {e}")

    def reboot_to_bios(self) -> bool:
        """重启到BIOS

        Returns:
            bool: 操作成功返回True，否则返回False
        """
        try:
            if sys.platform == "win32":
                # Windows 10/11 支持的UEFI重启到BIOS命令
                subprocess.run(["shutdown", "/r", "/fw", "/t", "0"], check=True)
                return True
            else:
                print("此功能仅支持Windows系统")
                return False
        except subprocess.CalledProcessError as e:
            print(f"重启到BIOS失败: {e}")
            return False
        except Exception as e:
            print(f"执行重启命令失败: {e}")
            return False


class OCToolsScanWorker(QThread):
    """超频工具扫描工作线程"""

    scan_completed = Signal(list)  # 扫描完成信号
    scan_error = Signal(str)  # 扫描错误信号

    def __init__(self):
        super().__init__()
        self.scanner = OCToolsScanner()

    def run(self):
        """执行扫描"""
        try:
            tools = self.scanner.scan_tools()
            self.scan_completed.emit(tools)
        except Exception as e:
            self.scan_error.emit(str(e))


# 单例模式
_oc_tools_scanner_instance = None


def get_oc_tools_scanner() -> OCToolsScanner:
    """获取超频工具扫描器实例（单例模式）

    Returns:
        OCToolsScanner: 超频工具扫描器实例
    """
    global _oc_tools_scanner_instance
    if _oc_tools_scanner_instance is None:
        _oc_tools_scanner_instance = OCToolsScanner()
    return _oc_tools_scanner_instance
