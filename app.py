# -*- coding: utf-8 -*-
"""
应用程序主类
"""
import sys
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt, QTranslator
from PySide6.QtGui import QIcon
from qfluentwidgets import FluentTranslator

from ui.main_window import MainWindow
from splash import show_splash_screen, create_app_icon
from admin_check import check_admin_permission


class HardwareToolboxApp(QApplication):
    """硬件工具箱应用程序类"""

    def __init__(self, argv):
        super().__init__(argv)

        # 设置应用程序属性
        self._setup_app_attributes()

        # 初始化翻译器
        self._setup_translator()

        # 初始化主窗口
        self.main_window = None

    def _setup_app_attributes(self):
        """设置应用程序属性"""
        self.setApplicationName("Windows硬件工具箱")
        self.setApplicationVersion("1.0.0")
        self.setOrganizationName("HardwareToolbox")
        self.setOrganizationDomain("hardwaretoolbox.local")

        # 设置应用程序图标
        icon = create_app_icon()
        self.setWindowIcon(icon)

        # 确保任务栏也显示图标
        if hasattr(self, "setApplicationDisplayName"):
            self.setApplicationDisplayName("Windows硬件工具箱")

        # Windows特定的任务栏图标设置
        try:
            import ctypes

            # 设置应用程序用户模型ID，确保任务栏图标正确显示
            ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(
                "HardwareToolbox.WindowsHardwareToolbox.1.0"
            )
        except Exception as e:
            print(f"设置任务栏图标失败: {e}")

        # 设置高DPI支持
        self.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling)
        self.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps)

        # 解决Dialog与FluentWindow兼容性问题
        self.setAttribute(Qt.ApplicationAttribute.AA_DontCreateNativeWidgetSiblings)

    def _setup_translator(self):
        """设置翻译器"""
        try:
            # 创建Fluent组件翻译器
            self.fluent_translator = FluentTranslator()
            self.installTranslator(self.fluent_translator)
        except Exception as e:
            print(f"翻译器设置失败: {e}")



    def run(self):
        """运行应用程序"""
        try:
            # 检查管理员权限（在显示界面前检查）
            if not check_admin_permission():
                print("需要管理员权限，程序退出")
                return 1

            # 显示启动动画并创建主窗口
            self.main_window = show_splash_screen(self, MainWindow, loading_time=1500)

            # 运行事件循环
            return self.exec()

        except Exception as e:
            print(f"应用程序运行失败: {e}")
            import traceback

            traceback.print_exc()
            return 1

    def get_main_window(self):
        """获取主窗口实例"""
        return self.main_window


def create_application():
    """创建应用程序实例"""
    return HardwareToolboxApp(sys.argv)


def main():
    """主函数"""
    app = create_application()
    return app.run()


if __name__ == "__main__":
    sys.exit(main())
