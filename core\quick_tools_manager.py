#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
快捷工具管理服务
提供快捷工具的执行和管理功能
"""

import subprocess
import sys
from typing import List, Dict, Optional
from PySide6.QtCore import QObject, QThread, Signal

from config.quick_tools import QUICK_TOOLS


class QuickToolsManager(QObject):
    """快捷工具管理器"""

    def __init__(self):
        super().__init__()
        self.tools_data = QUICK_TOOLS

    def get_categories(self) -> List[str]:
        """获取所有工具分类

        Returns:
            List[str]: 分类名称列表
        """
        return [category["category"] for category in self.tools_data]

    def get_tools_by_category(self, category_name: str) -> List[Dict[str, str]]:
        """根据分类获取工具列表

        Args:
            category_name: 分类名称

        Returns:
            List[Dict]: 工具信息列表
        """
        for category in self.tools_data:
            if category["category"] == category_name:
                return category["tools"]
        return []

    def get_all_tools(self) -> List[Dict[str, any]]:
        """获取所有工具（按分类组织）

        Returns:
            List[Dict]: 包含分类和工具的完整数据
        """
        return self.tools_data

    def is_dangerous_tool(self, tool_name: str) -> bool:
        """检查工具是否需要二次确认

        Args:
            tool_name: 工具名称

        Returns:
            bool: 如果是危险操作返回True
        """
        dangerous_tools = {"安全模式", "重启", "关机", "睡眠", "锁定计算机"}
        return tool_name in dangerous_tools

    def get_tool_description(self, tool_name: str) -> Optional[str]:
        """获取工具描述信息

        Args:
            tool_name: 工具名称

        Returns:
            str: 工具描述，如果没有描述返回None
        """
        for category in self.tools_data:
            for tool in category["tools"]:
                if tool["name"] == tool_name:
                    return tool.get("description")
        return None

    def execute_tool(self, tool_name: str) -> bool:
        """执行快捷工具

        Args:
            tool_name: 工具名称

        Returns:
            bool: 执行成功返回True，否则返回False
        """
        # 查找工具命令
        command = None
        for category in self.tools_data:
            for tool in category["tools"]:
                if tool["name"] == tool_name:
                    command = tool["command"]
                    break
            if command:
                break

        if not command:
            print(f"未找到工具: {tool_name}")
            return False

        try:
            # 特殊处理安全模式
            if tool_name == "安全模式":
                return self._enable_safe_mode()

            # 执行命令
            if sys.platform == "win32":
                # 特殊处理需要新窗口的工具
                if tool_name in ["命令提示符", "PowerShell"]:
                    if tool_name == "命令提示符":
                        subprocess.Popen(["start", "cmd"], shell=True)
                    elif tool_name == "PowerShell":
                        subprocess.Popen(["start", "powershell"], shell=True)
                else:
                    # 其他工具正常执行
                    subprocess.Popen(command, shell=True)
            else:
                # 其他平台
                subprocess.Popen(command.split())

            return True
        except Exception as e:
            print(f"执行工具失败 {tool_name}: {e}")
            return False

    def _enable_safe_mode(self) -> bool:
        """启用安全模式

        Returns:
            bool: 操作成功返回True
        """
        try:
            # 设置安全模式启动
            subprocess.run(
                ["bcdedit", "/set", "{current}", "safeboot", "minimal"],
                check=True,
                shell=True,
            )

            # 设置下次启动后自动恢复正常模式
            subprocess.run(
                ["bcdedit", "/set", "{current}", "safeboot", "minimal"],
                check=True,
                shell=True,
            )

            return True
        except subprocess.CalledProcessError as e:
            print(f"设置安全模式失败: {e}")
            return False
        except Exception as e:
            print(f"安全模式操作失败: {e}")
            return False


class QuickToolExecutor(QThread):
    """快捷工具执行工作线程"""

    execution_completed = Signal(str, bool)  # 执行完成信号 (tool_name, success)
    execution_error = Signal(str, str)  # 执行错误信号 (tool_name, error)

    def __init__(self, tool_name: str):
        super().__init__()
        self.tool_name = tool_name
        self.manager = QuickToolsManager()

    def run(self):
        """执行工具"""
        try:
            success = self.manager.execute_tool(self.tool_name)
            self.execution_completed.emit(self.tool_name, success)
        except Exception as e:
            self.execution_error.emit(self.tool_name, str(e))


# 单例模式
_quick_tools_manager_instance = None


def get_quick_tools_manager() -> QuickToolsManager:
    """获取快捷工具管理器实例（单例模式）

    Returns:
        QuickToolsManager: 快捷工具管理器实例
    """
    global _quick_tools_manager_instance
    if _quick_tools_manager_instance is None:
        _quick_tools_manager_instance = QuickToolsManager()
    return _quick_tools_manager_instance
