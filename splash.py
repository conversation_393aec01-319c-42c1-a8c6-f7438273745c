# -*- coding: utf-8 -*-
"""
启动画面模块
严格按照QFluentWidgets官方示例重新实现
"""

from PySide6.QtCore import QSize, QEventLoop, QTimer
from PySide6.QtGui import QIcon, QPixmap
from PySide6.QtWidgets import QApplication
from qfluentwidgets import SplashScreen


class MainWindowWithSplash:
    """
    带启动画面的主窗口包装类
    严格按照QFluentWidgets官方示例实现
    """

    def __init__(self, main_window_class):
        self.main_window_class = main_window_class
        self.main_window = None
        self.splash_screen = None

    def show_with_splash(self, loading_time=3000):
        """
        显示带启动动画的主窗口
        严格按照QFluentWidgets官方示例实现

        官方示例流程：
        1. 创建启动页面
        2. 在创建其他子页面前先显示主界面
        3. 创建子界面
        4. 隐藏启动页面

        Args:
            loading_time: 启动动画显示时间（毫秒）
        """
        # 1. 创建主窗口并设置基本属性
        self.main_window = self.main_window_class()
        # 不在这里设置窗口大小，让主窗口自己的_init_window方法处理
        self.main_window.setWindowTitle("Windows硬件工具箱")

        # 设置窗口图标
        icon = create_app_icon()
        self.main_window.setWindowIcon(icon)

        # 2. 创建启动页面（官方示例）
        self.splash_screen = SplashScreen(
            self.main_window.windowIcon(), self.main_window
        )
        self.splash_screen.setIconSize(QSize(102, 102))  # 官方示例使用102x102

        # 3. 在创建其他子页面前先显示主界面（官方推荐）
        self.main_window.show()

        # 4. 创建子界面（官方示例）
        self._create_sub_interfaces(loading_time)

        # 5. 隐藏启动页面（官方示例）
        self.splash_screen.finish()

        return self.main_window

    def _create_sub_interfaces(self, loading_time):
        """
        模拟创建子界面的过程
        使用官方示例的方式
        """
        loop = QEventLoop(self.main_window)
        QTimer.singleShot(loading_time, loop.quit)
        loop.exec()


def create_app_icon():
    """创建应用程序图标"""
    try:
        import os
        from PySide6.QtCore import Qt

        # 尝试加载图标文件
        icon_paths = [
            "assets/icon.ico",
            "assets/icon.png",
            "icon.ico",
            "icon.png",
        ]

        for path in icon_paths:
            try:
                if os.path.exists(path):
                    icon = QIcon(path)
                    if not icon.isNull():
                        return icon
            except Exception:
                continue

        # 如果没有找到图标文件，创建默认图标
        pixmap = QPixmap(64, 64)
        pixmap.fill(Qt.GlobalColor.blue)
        return QIcon(pixmap)

    except Exception as e:
        print(f"创建图标失败: {e}")
        return QIcon()


def show_splash_screen(app, main_window_class, loading_time=3000):
    """
    显示启动画面并创建主窗口
    严格按照QFluentWidgets官方示例实现

    Args:
        app: 应用程序实例
        main_window_class: 主窗口类
        loading_time: 启动画面显示时间（毫秒）

    Returns:
        主窗口实例
    """
    wrapper = MainWindowWithSplash(main_window_class)
    return wrapper.show_with_splash(loading_time)
