# -*- coding: utf-8 -*-
"""
Windows硬件工具箱 - 程序入口点

一个现代化的Windows硬件信息查看和系统优化工具
基于PySide6-Fluent-Widgets开发，采用Microsoft Fluent Design 2.0设计语言

功能特性：
- 硬件信息查看和导出
- 系统优化和清理
- 预装应用管理
- 超频工具集成
- 快捷系统工具
- 现代化Fluent Design界面

作者: HardwareToolbox Team
版本: 1.0.0
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from app import main

if __name__ == "__main__":
    # 设置工作目录为项目根目录
    os.chdir(project_root)

    # 启动应用程序
    sys.exit(main())
