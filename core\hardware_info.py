# -*- coding: utf-8 -*-
"""
硬件信息获取模块
提供获取系统硬件详细信息的功能
"""
import wmi
import datetime
import traceback
from typing import Dict, Any


def wmi_date_to_str(wmi_date):
    """将WMI日期格式转换为ISO 8601字符串"""
    if wmi_date is None:
        return None
    try:
        # 格式为 yyyymmddhhmmss.ffffff+|-zzz
        dt = datetime.datetime.strptime(wmi_date.split(".")[0], "%Y%m%d%H%M%S")
        return dt.isoformat()
    except (ValueError, IndexError):
        return str(wmi_date)


def get_system_info() -> Dict[str, Any]:
    """获取操作系统和基本系统信息"""
    try:
        c = wmi.WMI()
        os_info = c.Win32_OperatingSystem()[0]

        return {
            "caption": os_info.Caption,
            "version": os_info.Version,
            "os_architecture": os_info.OSArchitecture,
            "hostname": os_info.CSName,
            "last_boot_time": wmi_date_to_str(os_info.LastBootUpTime),
            "install_date": wmi_date_to_str(os_info.InstallDate),
        }
    except Exception as e:
        return {"error": str(e)}


def get_cpu_info() -> Dict[str, Any]:
    """获取CPU详细信息"""
    try:
        c = wmi.WMI()

        processors = []
        for p in c.Win32_Processor():
            processors.append(
                {
                    "device_id": p.DeviceID,
                    "name": p.Name,
                    "manufacturer": p.Manufacturer,
                    "current_clock_speed": f"{p.CurrentClockSpeed} MHz",
                    "max_clock_speed": f"{p.MaxClockSpeed} MHz",
                    "cores": p.NumberOfCores,
                    "logical_processors": p.NumberOfLogicalProcessors,
                    "architecture": p.Architecture,
                    "socket_designation": p.SocketDesignation,
                }
            )

        return {
            "processors": processors,
        }
    except Exception as e:
        return {"error": str(e)}


def get_memory_info() -> Dict[str, Any]:
    """获取内存信息"""
    try:
        c = wmi.WMI()
        physical_memory = []
        total_capacity = 0

        for mem in c.Win32_PhysicalMemory():
            capacity_gb = int(mem.Capacity) / (1024**3)
            total_capacity += capacity_gb
            physical_memory.append(
                {
                    "capacity": f"{capacity_gb:.2f} GB",
                    "part_number": mem.PartNumber.strip() if mem.PartNumber else "未知",
                    "speed": f"{mem.Speed} MHz" if mem.Speed else "未知",
                    "manufacturer": mem.Manufacturer if mem.Manufacturer else "未知",
                    "device_locator": (
                        mem.DeviceLocator if mem.DeviceLocator else "未知"
                    ),
                }
            )

        return {
            "total_capacity": f"{total_capacity:.2f} GB",
            "physical_slots": physical_memory,
        }
    except Exception as e:
        return {"error": str(e)}


def get_disk_info() -> Dict[str, Any]:
    """获取磁盘信息"""
    try:
        c = wmi.WMI()
        physical_disks = []

        for disk in c.Win32_DiskDrive():
            physical_disks.append(
                {
                    "model": disk.Model,
                    "size": f"{int(disk.Size) / (1024**3):.2f} GB",
                    "interface_type": disk.InterfaceType,
                    "serial": (
                        disk.SerialNumber.strip() if disk.SerialNumber else "未知"
                    ),
                    "status": disk.Status if disk.Status else "未知",
                }
            )

        return {
            "physical_disks": physical_disks,
        }
    except Exception as e:
        return {"error": str(e)}


def get_gpu_info() -> Dict[str, Any]:
    """获取GPU信息"""
    try:
        c = wmi.WMI()
        gpus = []

        for gpu in c.Win32_VideoController():
            pnp_id = gpu.PNPDeviceID
            if pnp_id and "PCI" in pnp_id:
                gpus.append(
                    {
                        "name": gpu.Name,
                        "driver_version": (
                            gpu.DriverVersion if gpu.DriverVersion else "未知"
                        ),
                        "current_refresh_rate": (
                            f"{gpu.CurrentRefreshRate} Hz"
                            if gpu.CurrentRefreshRate
                            else "未知"
                        ),
                        "resolution": (
                            f"{gpu.CurrentHorizontalResolution}x{gpu.CurrentVerticalResolution}"
                            if gpu.CurrentHorizontalResolution
                            else "未知"
                        ),
                        "adapter_ram": (
                            f"{int(gpu.AdapterRAM) / (1024**3):.2f} GB"
                            if gpu.AdapterRAM
                            else "未知"
                        ),
                        "video_processor": (
                            gpu.VideoProcessor if gpu.VideoProcessor else "未知"
                        ),
                    }
                )
        return {"gpus": gpus}
    except Exception as e:
        return {"error": str(e)}


def get_motherboard_info() -> Dict[str, Any]:
    """获取主板和BIOS信息"""
    try:
        c = wmi.WMI()
        baseboard = c.Win32_BaseBoard()[0]
        bios = c.Win32_BIOS()[0]
        return {
            "motherboard": {
                "manufacturer": (
                    baseboard.Manufacturer if baseboard.Manufacturer else "未知"
                ),
                "product": baseboard.Product if baseboard.Product else "未知",
                "serial_number": (
                    baseboard.SerialNumber.strip() if baseboard.SerialNumber else "未知"
                ),
            },
            "bios": {
                "manufacturer": bios.Manufacturer if bios.Manufacturer else "未知",
                "version": bios.SMBIOSBIOSVersion if bios.SMBIOSBIOSVersion else "未知",
                "release_date": wmi_date_to_str(bios.ReleaseDate),
            },
        }
    except Exception as e:
        return {"error": str(e)}


def get_all_hardware_info() -> Dict[str, Any]:
    """获取所有硬件信息"""
    return {
        "system": get_system_info(),
        "motherboard": get_motherboard_info(),
        "cpu": get_cpu_info(),
        "memory": get_memory_info(),
        "disk": get_disk_info(),
        "gpu": get_gpu_info(),
    }
