#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
快捷工具视图
提供快捷工具的分类显示和执行界面
"""

from PySide6.QtCore import Qt
from PySide6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QScrollArea,
)

from qfluentwidgets import (
    HeaderCardWidget,
    CardWidget,
    BodyLabel,
    CaptionLabel,
    TitleLabel,
    InfoBar,
    InfoBarPosition,
    FluentIcon,
    MessageBox,
    FlowLayout,
    SingleDirectionScrollArea,
)

from core.quick_tools_manager import get_quick_tools_manager, QuickToolExecutor


class QuickToolCard(CardWidget):
    """快捷工具卡片"""

    def __init__(self, tool_info: dict):
        super().__init__()
        self.tool_info = tool_info
        self.manager = get_quick_tools_manager()
        self._init_ui()

    def _init_ui(self):
        """初始化界面"""
        self.setObjectName(f"ToolCard_{self.tool_info['name']}")
        self.setFixedSize(160, 40)  # 采用水平布局的尺寸

        # 按钮内部布局
        layout = QHBoxLayout(self)
        layout.setContentsMargins(12, 0, 12, 0)  # 左右12px边距
        layout.setSpacing(8)

        # 工具名称标签
        name_label = BodyLabel(self.tool_info["name"])
        name_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        name_label.setStyleSheet("font-size: 13px; font-weight: 500;")
        layout.addWidget(name_label)

        # 点击事件
        self.clicked.connect(self._on_clicked)

    def _on_clicked(self):
        """卡片点击事件"""
        tool_name = self.tool_info["name"]

        # 检查是否需要二次确认
        if self.manager.is_dangerous_tool(tool_name):
            self._show_confirmation_dialog(tool_name)
        else:
            self._execute_tool(tool_name)

    def _show_confirmation_dialog(self, tool_name: str):
        """显示确认对话框"""
        description = self.manager.get_tool_description(tool_name)

        if description:
            content = f"即将执行: {tool_name}\n\n{description}\n\n是否继续？"
        else:
            content = f"即将执行: {tool_name}\n\n此操作可能影响系统状态，是否继续？"

        msg_box = MessageBox(f"确认执行 - {tool_name}", content, self.window())

        if msg_box.exec():
            self._execute_tool(tool_name)

    def _execute_tool(self, tool_name: str):
        """执行工具"""
        # 使用线程执行工具，避免阻塞UI
        self.executor = QuickToolExecutor(tool_name)
        self.executor.execution_completed.connect(self._on_execution_completed)
        self.executor.execution_error.connect(self._on_execution_error)
        self.executor.start()

        # 显示执行状态
        InfoBar.info(
            title="正在执行",
            content=f"正在启动 {tool_name}...",
            orient=Qt.Orientation.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=2000,
            parent=self.window(),
        )

    def _on_execution_completed(self, tool_name: str, success: bool):
        """执行完成处理"""
        if success:
            InfoBar.success(
                title="执行成功",
                content=f"{tool_name} 已启动",
                orient=Qt.Orientation.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=2000,
                parent=self.window(),
            )
        else:
            InfoBar.error(
                title="执行失败",
                content=f"无法启动 {tool_name}",
                orient=Qt.Orientation.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=3000,
                parent=self.window(),
            )

    def _on_execution_error(self, tool_name: str, error: str):
        """执行错误处理"""
        InfoBar.error(
            title="执行错误",
            content=f"启动 {tool_name} 时发生错误: {error}",
            orient=Qt.Orientation.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=3000,
            parent=self.window(),
        )


class QuickToolsView(SingleDirectionScrollArea):
    """快捷工具视图"""

    def __init__(self):
        super().__init__(orient=Qt.Orientation.Vertical)
        self.setObjectName("QuickToolsView")  # 设置对象名称，FluentWindow要求
        self.setWidgetResizable(True)
        self.enableTransparentBackground()
        self.setStyleSheet("QScrollArea { border: none; background: transparent; }")
        self.viewport().setStyleSheet("background: transparent;")

        self.manager = get_quick_tools_manager()
        self._init_ui()

    def _init_ui(self):
        """初始化界面"""
        # 创建内容容器
        content_widget = QWidget()
        content_widget.setStyleSheet("background: transparent;")

        layout = QVBoxLayout(content_widget)
        layout.setContentsMargins(24, 24, 24, 24)
        layout.setSpacing(20)  # 统一为20px，与其他视图保持一致

        # 页面标题 - 使用TitleLabel + CaptionLabel标准格式
        title_widget = QWidget()
        title_layout = QVBoxLayout(title_widget)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(4)

        title_label = TitleLabel("快捷工具", self)
        subtitle_label = CaptionLabel("快速访问常用系统工具和功能", self)

        title_layout.addWidget(title_label)
        title_layout.addWidget(subtitle_label)

        layout.addWidget(title_widget)

        # 创建滚动区域
        self._create_scroll_area(layout)

        # 设置为滚动区域的内容
        self.setWidget(content_widget)

    def _create_scroll_area(self, parent_layout):
        """创建滚动区域"""
        scroll_area = SingleDirectionScrollArea(orient=Qt.Orientation.Vertical)
        scroll_area.setWidgetResizable(True)
        scroll_area.enableTransparentBackground()
        scroll_area.setStyleSheet(
            "QScrollArea { border: none; background: transparent; }"
        )
        scroll_area.viewport().setStyleSheet("background: transparent;")

        # 创建内容容器
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(16)

        # 添加工具分类
        self._create_tool_categories(content_layout)

        scroll_area.setWidget(content_widget)
        parent_layout.addWidget(scroll_area)

    def _create_tool_categories(self, parent_layout):
        """创建工具分类"""
        tools_data = self.manager.get_all_tools()

        for category_data in tools_data:
            category_name = category_data["category"]
            tools = category_data["tools"]

            # 创建分类标题卡片 - 使用HeaderCardWidget
            category_card = HeaderCardWidget()
            category_card.setTitle(category_name)

            # 工具网格容器
            tools_container = QWidget()
            tools_layout = FlowLayout(tools_container)
            tools_layout.setContentsMargins(0, 0, 0, 0)
            tools_layout.setSpacing(12)

            # 添加工具卡片
            for tool in tools:
                tool_card = QuickToolCard(tool)
                tools_layout.addWidget(tool_card)

            # 将工具容器添加到HeaderCardWidget的viewLayout
            category_card.viewLayout.addWidget(tools_container)
            parent_layout.addWidget(category_card)
