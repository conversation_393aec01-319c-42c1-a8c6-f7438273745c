# -*- coding: utf-8 -*-
"""
主窗口模块
"""
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QIcon
from PySide6.QtWidgets import QApplication, QWidget
from qfluentwidgets import FluentIcon as FIF
from qfluentwidgets import FluentWindow, NavigationItemPosition, SubtitleLabel, setFont

from ui.views.hardware_view import HardwareView
from ui.views.optimization_view import OptimizationView
from ui.views.overclocking_view import OverclockingView
from ui.views.preinstalled_view import PreinstalledView
from ui.views.quick_tools_view import QuickToolsView
from ui.views.settings_view import SettingsView


class MainWindow(FluentWindow):
    """主窗口类"""

    def __init__(self):
        super().__init__()

        # 初始化界面
        self._init_interfaces()
        self._init_navigation()
        self._init_window()

    def _init_interfaces(self):
        """初始化各个功能界面"""
        # 硬件信息界面
        from ui.views.hardware_view import HardwareView

        self.hardware_interface = HardwareView(self)
        self.hardware_interface.setObjectName("hardware-interface")

        # 系统优化界面
        from ui.views.optimization_view import OptimizationView

        self.optimization_interface = OptimizationView(self)
        self.optimization_interface.setObjectName("optimization-interface")

        # 预装应用管理界面
        from ui.views.preinstalled_view import PreinstalledView

        self.preinstalled_interface = PreinstalledView()
        self.preinstalled_interface.setObjectName("preinstalled-interface")

        # 超频工具界面
        from ui.views.overclocking_view import OverclockingView

        self.overclocking_interface = OverclockingView()
        self.overclocking_interface.setObjectName("overclocking-interface")

        # 快捷工具界面
        from ui.views.quick_tools_view import QuickToolsView

        self.quick_tools_interface = QuickToolsView()
        self.quick_tools_interface.setObjectName("quick-tools-interface")

        # 设置关于界面
        from ui.views.settings_view import SettingsView

        self.settings_interface = SettingsView()
        self.settings_interface.setObjectName("settings-interface")

    def _init_navigation(self):
        """初始化导航栏"""
        # 添加硬件信息界面
        self.addSubInterface(
            self.hardware_interface,
            FIF.FOLDER,
            "硬件信息",
            NavigationItemPosition.TOP,
        )

        # 添加系统优化界面
        self.addSubInterface(
            self.optimization_interface,
            FIF.SPEED_OFF,
            "系统优化",
            NavigationItemPosition.TOP,
        )

        # 添加预装应用界面
        self.addSubInterface(
            self.preinstalled_interface,
            FIF.APPLICATION,
            "预装应用",
            NavigationItemPosition.TOP,
        )

        # 添加超频工具界面
        self.addSubInterface(
            self.overclocking_interface,
            FIF.GAME,
            "超频工具",
            NavigationItemPosition.TOP,
        )

        # 添加快捷工具界面
        self.addSubInterface(
            self.quick_tools_interface,
            FIF.COMMAND_PROMPT,
            "快捷工具",
            NavigationItemPosition.TOP,
        )

        # 添加分隔符
        self.navigationInterface.addSeparator()

        # 添加设置界面到底部
        self.addSubInterface(
            self.settings_interface,
            FIF.SETTING,
            "设置关于",
            NavigationItemPosition.BOTTOM,
        )

        # 设置导航栏属性
        self.navigationInterface.setExpandWidth(280)
        self.navigationInterface.setMinimumExpandWidth(900)

    def _init_window(self):
        """初始化窗口属性"""
        # 调整窗口大小以适应一行四个超频工具卡片
        # 计算：导航栏150px + 内容边距48px + 4个卡片(200px*4) + 3个间距(16px*3) + 滚动条20px = 1066px
        # 使用标准尺寸1200x800，既满足功能需求又符合标准规范
        self.resize(1280, 720)
        self.setWindowTitle("Windows硬件工具箱")

        # 设置窗口图标
        try:
            from splash import create_app_icon

            icon = create_app_icon()
            self.setWindowIcon(icon)

            # 确保任务栏图标正确显示
            if hasattr(QApplication.instance(), "setWindowIcon"):
                QApplication.instance().setWindowIcon(icon)

        except Exception as e:
            print(f"设置窗口图标失败: {e}")
            self.setWindowIcon(QIcon())

        # 居中显示
        self._center_window()

    def _center_window(self):
        """将窗口居中显示"""
        try:
            screen = QApplication.primaryScreen()
            if screen:
                screen_geometry = screen.availableGeometry()
                window_geometry = self.frameGeometry()
                center_point = screen_geometry.center()
                window_geometry.moveCenter(center_point)
                self.move(window_geometry.topLeft())
        except Exception as e:
            print(f"窗口居中失败: {e}")

    def closeEvent(self, event):
        """窗口关闭事件"""
        super().closeEvent(event)
        """窗口关闭事件"""
        # 清理硬件信息管理器资源
        if hasattr(self, "hardware_interface") and hasattr(
            self.hardware_interface, "hardware_manager"
        ):
            self.hardware_interface.hardware_manager.cleanup()
        super().closeEvent(event)
