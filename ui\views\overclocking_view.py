#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
超频工具视图
提供超频工具管理和启动界面
"""

from PySide6.QtCore import Qt, QSize, QTimer
from PySide6.QtGui import QMouseEvent
from PySide6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QGridLayout,
    QScrollArea,
    QFrame,
    QSizePolicy,
)

from qfluentwidgets import (
    HeaderCardWidget,
    CardWidget,
    ElevatedCardWidget,
    PushButton,
    BodyLabel,
    CaptionLabel,
    TitleLabel,
    InfoBar,
    InfoBarPosition,
    FluentIcon,
    MessageBox,
    IconWidget,
    SingleDirectionScrollArea,
)

from core.oc_tools_scanner import get_oc_tools_scanner, OCToolsScanWorker


class OCToolCard(ElevatedCardWidget):
    """超频工具卡片"""

    def __init__(self, tool_info: dict):
        super().__init__()
        self.tool_info = tool_info
        self._init_ui()

    def _init_ui(self):
        """初始化界面"""
        # 固定卡片大小
        self.setFixedSize(140, 120)  # 固定宽度140，高度120

        layout = QVBoxLayout(self)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(8)

        # 工具图标
        icon_widget = IconWidget(self.tool_info.get("icon", FluentIcon.APPLICATION))
        icon_widget.setFixedSize(48, 48)
        layout.addWidget(icon_widget, 0, Qt.AlignmentFlag.AlignCenter)

        # 工具名称
        name_label = BodyLabel(self.tool_info["name"])
        name_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        name_label.setWordWrap(True)
        layout.addWidget(name_label)

        layout.addStretch()

        # 设置卡片可点击样式
        self.setStyleSheet(
            """
            OCToolCard {
                border: 2px solid transparent;
            }
            OCToolCard:hover {
                border: 2px solid rgba(0, 120, 212, 0.8);
            }
        """
        )

    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self._launch_tool()
        super().mousePressEvent(event)

    def _launch_tool(self):
        """启动工具"""
        scanner = get_oc_tools_scanner()
        success = scanner.launch_tool(
            self.tool_info["path"], self.tool_info.get("is_gui", True)
        )

        if success:
            InfoBar.success(
                title="启动成功",
                content=f"已启动 {self.tool_info['name']}",
                orient=Qt.Orientation.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=2000,
                parent=self.window(),
            )
        else:
            InfoBar.error(
                title="启动失败",
                content=f"无法启动 {self.tool_info['name']}",
                orient=Qt.Orientation.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=3000,
                parent=self.window(),
            )


class OverclockingView(SingleDirectionScrollArea):
    """超频工具视图"""

    def __init__(self):
        super().__init__(orient=Qt.Orientation.Vertical)
        self.setObjectName("OverclockingView")  # 设置对象名称，FluentWindow要求
        self.setWidgetResizable(True)
        self.enableTransparentBackground()
        self.setStyleSheet("QScrollArea { border: none; background: transparent; }")
        self.viewport().setStyleSheet("background: transparent;")

        self.scanner = get_oc_tools_scanner()
        self.scan_worker = None
        self.tools = []
        self.is_loaded = False  # 标记是否已加载
        self.current_cards_per_row = 0  # 跟踪当前列数，避免不必要的重新排列

        # 布局参数
        self.fixed_card_width = 140  # 固定卡片宽度
        self.fixed_card_height = 120  # 固定卡片高度
        self.card_spacing = 16  # 卡片间距
        self.content_margins = 48  # 主布局边距 (24*2)

        # 延迟调整布局的定时器
        self.resize_timer = QTimer()
        self.resize_timer.setSingleShot(True)
        self.resize_timer.timeout.connect(self._adjust_layout)

        self._init_ui()
        # 不在构造函数中立即扫描，而是在显示时扫描

    def _init_ui(self):
        """初始化界面"""
        # 创建内容容器
        content_widget = QWidget()
        content_widget.setStyleSheet("background: transparent;")
        # 设置大小策略，防止内容被拉伸
        content_widget.setSizePolicy(
            QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Minimum
        )

        layout = QVBoxLayout(content_widget)
        layout.setContentsMargins(24, 24, 24, 24)
        layout.setSpacing(20)

        # 页面标题 - 使用TitleLabel + CaptionLabel标准格式
        title_widget = QWidget()
        title_layout = QVBoxLayout(title_widget)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(4)

        title_label = TitleLabel("超频工具管理", self)
        subtitle_label = CaptionLabel("管理和启动各种超频工具，优化硬件性能", self)

        title_layout.addWidget(title_label)
        title_layout.addWidget(subtitle_label)

        layout.addWidget(title_widget)

        # 快捷操作区域
        self._create_quick_actions(layout)

        # 工具网格区域
        self._create_tools_grid(layout)

        # 设置为滚动区域的内容
        self.setWidget(content_widget)

    def _create_quick_actions(self, parent_layout):
        """创建快捷操作区域"""
        actions_card = CardWidget()
        actions_layout = QHBoxLayout(actions_card)
        actions_layout.setContentsMargins(16, 16, 16, 16)
        actions_layout.setSpacing(12)

        # 一键进入BIOS按钮
        bios_btn = PushButton("一键进入BIOS")
        bios_btn.setIcon(FluentIcon.POWER_BUTTON)
        bios_btn.clicked.connect(self._reboot_to_bios)
        actions_layout.addWidget(bios_btn)

        # 打开工具文件夹按钮
        folder_btn = PushButton("打开工具文件夹")
        folder_btn.setIcon(FluentIcon.FOLDER)
        folder_btn.clicked.connect(self._open_tools_folder)
        actions_layout.addWidget(folder_btn)

        actions_layout.addStretch()

        # 创建带边距的容器以与工具网格对齐
        actions_container = QWidget()
        actions_container_layout = QHBoxLayout(actions_container)
        actions_container_layout.setContentsMargins(
            0, 0, 0, 0
        )  # 不需要额外边距，因为主布局已有24px边距
        actions_container_layout.addWidget(actions_card)

        parent_layout.addWidget(actions_container)

    def _create_tools_grid(self, parent_layout):
        """创建工具网格区域"""
        # 直接创建工具容器，使用基类的滚动功能
        self.tools_container = QWidget()
        self.tools_layout = QGridLayout(self.tools_container)
        self.tools_layout.setContentsMargins(0, 0, 0, 0)  # 不设置边距，使用主布局的边距
        self.tools_layout.setHorizontalSpacing(16)
        self.tools_layout.setVerticalSpacing(16)

        parent_layout.addWidget(self.tools_container)

    def _calculate_cards_per_row(self):
        """计算每行能显示的卡片数量"""
        # 获取可用宽度（减去导航栏、边距、滚动条等）
        available_width = self.width() - self.content_margins

        # 如果宽度太小，最少显示1个
        if available_width < self.fixed_card_width:
            return 1

        # 计算能容纳的卡片数量：(可用宽度 + 间距) / (卡片宽度 + 间距)
        cards_per_row = max(
            1,
            (available_width + self.card_spacing)
            // (self.fixed_card_width + self.card_spacing),
        )

        return cards_per_row

    def _adjust_layout(self):
        """智能调整布局以适应窗口大小 - 只在列数变化时才重新排列"""
        if not self.tools:
            return

        # 计算每行卡片数量
        cards_per_row = self._calculate_cards_per_row()

        # 只在列数真正发生变化时才重新排列，避免不必要的布局重建
        if cards_per_row == self.current_cards_per_row:
            return

        # 更新当前列数
        self.current_cards_per_row = cards_per_row

        # 简化逻辑：直接重新创建网格，但避免频繁调用
        self._update_tools_grid()

    def showEvent(self, event):
        """界面显示时的事件处理"""
        super().showEvent(event)
        # 只在第一次显示时扫描工具
        if not self.is_loaded:
            self.is_loaded = True
            self._scan_tools()

    def resizeEvent(self, event):
        """窗口大小变化事件"""
        super().resizeEvent(event)
        # 延迟调整布局，避免频繁重绘
        self.resize_timer.start(100)

    def _scan_tools(self):
        """扫描超频工具"""
        if self.scan_worker and self.scan_worker.isRunning():
            return

        # 清空现有工具
        self._clear_tools_grid()

        # 显示扫描状态
        InfoBar.info(
            title="扫描中",
            content="正在扫描超频工具...",
            orient=Qt.Orientation.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=2000,
            parent=self,
        )

        # 启动扫描工作线程
        self.scan_worker = OCToolsScanWorker()
        self.scan_worker.scan_completed.connect(self._on_scan_completed)
        self.scan_worker.scan_error.connect(self._on_scan_error)
        self.scan_worker.start()

    def _on_scan_completed(self, tools):
        """扫描完成处理"""
        self.tools = tools
        self._update_tools_grid()

        InfoBar.success(
            title="扫描完成",
            content=f"发现 {len(tools)} 个超频工具",
            orient=Qt.Orientation.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=2000,
            parent=self,
        )

    def _on_scan_error(self, error):
        """扫描错误处理"""
        InfoBar.error(
            title="扫描失败",
            content=f"扫描超频工具时发生错误: {error}",
            orient=Qt.Orientation.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=3000,
            parent=self,
        )

    def _update_tools_grid(self):
        """更新工具网格 - 优化版本，减少布局重建"""
        self._clear_tools_grid()

        if not self.tools:
            # 显示空状态
            empty_label = BodyLabel("未发现超频工具\n请将工具放入OCTools文件夹中")
            empty_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            empty_label.setStyleSheet("font-size: 14px;")
            self.tools_layout.addWidget(empty_label, 0, 0)
            self.current_cards_per_row = 0
        else:
            # 计算每行卡片数量
            cards_per_row = self._calculate_cards_per_row()
            self.current_cards_per_row = cards_per_row

            # 添加工具卡片到网格布局
            for i, tool_info in enumerate(self.tools):
                tool_card = OCToolCard(tool_info)
                row = i // cards_per_row
                col = i % cards_per_row
                self.tools_layout.addWidget(tool_card, row, col)

    def _clear_tools_grid(self):
        """清空工具网格"""
        # 清空网格布局中的所有组件
        for i in reversed(range(self.tools_layout.count())):
            item = self.tools_layout.itemAt(i)
            if item:
                widget = item.widget()
                if widget:
                    self.tools_layout.removeWidget(widget)
                    widget.deleteLater()

    def _reboot_to_bios(self):
        """重启到BIOS"""
        # 二次确认对话框
        msg_box = MessageBox(
            "确认重启到BIOS",
            "此操作将立即重启计算机并进入BIOS设置。\n请确保已保存所有工作。\n\n是否继续？",
            self,
        )

        if msg_box.exec():
            success = self.scanner.reboot_to_bios()
            if not success:
                InfoBar.error(
                    title="操作失败",
                    content="无法重启到BIOS，请检查系统权限",
                    orient=Qt.Orientation.Horizontal,
                    isClosable=True,
                    position=InfoBarPosition.TOP,
                    duration=3000,
                    parent=self,
                )

    def _open_tools_folder(self):
        """打开工具文件夹"""
        self.scanner.open_tools_directory()

        InfoBar.info(
            title="文件夹已打开",
            content="OCTools文件夹已在文件管理器中打开",
            orient=Qt.Orientation.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=2000,
            parent=self,
        )
